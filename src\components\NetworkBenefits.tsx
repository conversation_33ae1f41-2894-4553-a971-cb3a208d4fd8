import React from "react";
import {
  Shield,
  Users,
  BarChart3,
  Zap,
  Globe,
  CheckCircle,
  TrendingUp,
} from "lucide-react";
import { Button } from "./ui";
import { scrollToSection } from "../utils/scroll";

const NetworkBenefits: React.FC = () => {
  const networkBenefits = [
    {
      stakeholder: "Farmers",
      icon: Users,
      benefits: [
        "Tokenize your produce",
        "Transparent, real-time pricing",
        "Instant digital payments",
        "Access global buyers from your local cooperative",
      ],
      color: "green",
    },
    {
      stakeholder: "Traders & Cooperatives",
      icon: BarChart3,
      benefits: [
        "Tokenise inventory for instant, tamper-proof certification",
        "Manage trades, payments, and documents in one platform",
        "Access a verified, international pool of manufacturers",
        "Improve margins by automating settlement and reducing manual errors",
      ],
      color: "blue",
    },
    {
      stakeholder: "Manufacturers & Buyers",
      icon: Globe,
      benefits: [
        "Bid directly on verified, tokenised commodity lots",
        "Monitor origin, logistics, and quality via smart contracts",
        "Minimise sourcing risk with end-to-end compliance visibility",
        "Ensure ESG integrity, down to the farmer and the field",
      ],
      color: "amber",
    },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      emerald: {
        bg: "bg-emerald-100",
        text: "text-emerald-600",
        border: "border-emerald-200",
      },
      blue: {
        bg: "bg-blue-100",
        text: "text-blue-600",
        border: "border-blue-200",
      },
      amber: {
        bg: "bg-amber-100",
        text: "text-amber-600",
        border: "border-amber-200",
      },
      purple: {
        bg: "bg-purple-100",
        text: "text-purple-600",
        border: "border-purple-200",
      },
      green: {
        bg: "bg-green-100",
        text: "text-green-600",
        border: "border-green-200",
      },
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.emerald;
  };

  return (
    <div id="network-benefits" className="bg-white py-12 sm:py-16 lg:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Network Benefits */}
        <div>
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
              Be First to Trade Digitally
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto px-2">
              Unlock early access to a platform designed for faster payments,
              verified trade, and real impact across the entire agricultural
              supply chain.
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {networkBenefits.map((network, index) => {
              const colorClasses = getColorClasses(network.color);
              const IconComponent = network.icon;

              return (
                <div
                  key={index}
                  className={`bg-gradient-to-br from-white to-gray-50 border-2 ${colorClasses.border} rounded-xl sm:rounded-2xl p-6 sm:p-8 hover:shadow-lg transition-all duration-300`}
                >
                  <div className="text-center mb-4 sm:mb-6">
                    <div
                      className={`${colorClasses.bg} w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4`}
                    >
                      <IconComponent
                        className={`h-6 w-6 sm:h-8 sm:w-8 ${colorClasses.text}`}
                      />
                    </div>
                    <h4 className="text-lg sm:text-xl font-bold text-gray-900">
                      {network.stakeholder}
                    </h4>
                  </div>

                  <div className="space-y-2 sm:space-y-3">
                    {network.benefits.map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-start">
                        <CheckCircle
                          className={`h-4 w-4 ${colorClasses.text} mr-2 sm:mr-3 flex-shrink-0 mt-0.5`}
                        />
                        <span className="text-sm sm:text-base text-gray-700 leading-relaxed">
                          {benefit}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Technology Highlights */}
        <div className="mt-12 sm:mt-16 lg:mt-20 bg-gradient-to-r from-emerald-50 to-amber-50 rounded-xl sm:rounded-2xl p-6 sm:p-8">
          <div className="text-center mb-6 sm:mb-8">
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
              Powered by Cutting-Edge Technology
            </h3>
            <p className="text-sm sm:text-base text-gray-600 max-w-3xl mx-auto px-2">
              Agritram runs on a secure, scalable blockchain infrastructure.
              Designed to bring speed, trust, and financial access to every
              transaction in the agricultural trade network.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            <div className="text-center p-4 sm:p-0">
              <Zap className="h-6 w-6 sm:h-8 sm:w-8 text-emerald-600 mx-auto mb-2 sm:mb-3" />
              <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1 sm:mb-2">
                Instant Settlements
              </h4>
              <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                Receive payments immediately upon verification.
              </p>
            </div>
            <div className="text-center p-4 sm:p-0">
              <Shield className="h-6 w-6 sm:h-8 sm:w-8 text-emerald-600 mx-auto mb-2 sm:mb-3" />
              <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1 sm:mb-2">
                Secure Transactions
              </h4>
              <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                Smart contracts lock in pricing, risk-sharing, and
                proof-of-delivery automatically.
              </p>
            </div>
            <div className="text-center p-4 sm:p-0">
              <Globe className="h-6 w-6 sm:h-8 sm:w-8 text-emerald-600 mx-auto mb-2 sm:mb-3" />
              <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1 sm:mb-2">
                Global Scale
              </h4>
              <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                Multi-region deployment supports cross-border transactions and
                compliance from Day 1.
              </p>
            </div>
            <div className="text-center p-4 sm:p-0">
              <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-emerald-600 mx-auto mb-2 sm:mb-3" />
              <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1 sm:mb-2">
                Increased Profits
              </h4>
              <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                Eliminate middlemen and maximise your earnings.
              </p>
            </div>
          </div>

          {/* Notification Signup */}
          <div className="pt-6 sm:pt-8 text-center">
            <p className="text-sm sm:text-base text-theme-primary-text opacity-80 mb-4 sm:mb-6 px-2">
              Be the first to know when Agritram launches. Join our exclusive
              waitlist.
            </p>

            <p className="text-xs sm:text-sm text-center text-theme-primary-text opacity-70 mb-4 sm:mb-6 px-2">
              Join 600,000+ farmers, 300+ traders and 100+ manufacturers already
              signed up for early access
            </p>
            <div className="flex justify-center">
              <Button
                variant="primary"
                size="lg"
                onClick={() => scrollToSection("#early-access")}
                className="w-full sm:w-auto max-w-xs"
              >
                Get Early Access
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkBenefits;
