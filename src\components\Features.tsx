import React from "react";
import { CheckCircle, Coins, Shield, MapPin, ShoppingCart } from "lucide-react";

const Features: React.FC = () => {
  const features = [
    {
      icon: Shield,
      title: "Smart Contract Settlements",
      description:
        "Agritram automates payments, compliance, and document verification with programmable contracts.",
      color: "bg-blue-50 text-blue-600",
      benefits: [
        "Funds are released in stages based on Incoterms and proof-of-delivery",
        "Both parties define who holds risk at each transport point",
        "Every transaction is timestamped and tamper-proof",
        "No need for third-party escrow or manual approvals",
      ],
    },
    {
      icon: Coins,
      title: "Commodity Tokenisation",
      description:
        "Each verified commodity lot is converted into a digital asset, ready for trade.",
      color: "bg-yellow-50 text-yellow-600",
      benefits: [
        "Co-ops or warehouses generate tokenised certificates of reserve",
        "Tokens reflect real-world quantity, grade, and location",
        "Manufacturers can trace product origin down to the farmer",
        "Tokens can be used for trade, borrowing, or inventory tracking",
      ],
    },
    {
      icon: ShoppingCart,
      title: "Integrated Trade & Bidding System",
      description:
        "Agritram offers a secure digital marketplace for trading tokenised goods.",
      color: "bg-green-50 text-green-600",
      benefits: [
        "Manufacturers bid live on listed commodities from verified sellers",
        "Full trade flow from bid to delivery is handled in-platform",
        "Standardised documentation for customs and logistics",
        "Transparent price discovery replaces opaque middlemen",
      ],
    },
    {
      icon: MapPin,
      title: "Real-Time Traceability & Compliance Tools",
      description:
        "Track every stage of trade from farm to port with full digital oversight and accountability.",
      color: "bg-purple-50 text-purple-600",
      benefits: [
        "Every lot is linked to a verified origin, cooperative, and GPS location",
        "Immutable trade records support ESG and due diligence reporting",
        "Certificates and documents are uploaded, stored, and verified on-chain",
        "Manufacturers access sourcing history and compliance in one dashboard",
      ],
    },
  ];

  return (
    <section
      id="features"
      className="py-12 sm:py-16 lg:py-24"
      style={{ backgroundColor: "#fff5ea" }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-black mb-4 sm:mb-6 px-2">
            Platform <span style={{ color: "#2d4d31" }}>Features</span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-2">
            Built for the future of trade. Secure, seamless, and fair for
            everyone in the agricultural supply chain.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
          {features.map((feature, index) => {
            return (
              <div
                key={index}
                className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-[#2D4D31]/20 hover:-translate-y-1 sm:hover:-translate-y-2 group hover:border-emerald-200"
              >
                <div className="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-4">
                  <div
                    className={`w-12 h-12 sm:w-16 sm:h-16 rounded-full ${feature.color} flex items-center justify-center mx-auto sm:mx-0 flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <feature.icon className="w-6 h-6 sm:w-8 sm:h-8" />
                  </div>
                  <div className="flex-1 text-center sm:text-left">
                    <h3 className="text-lg sm:text-xl font-bold text-black mb-3 sm:mb-4">
                      {feature.title}
                    </h3>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed mb-4">
                      {feature.description}
                    </p>
                    <div className="space-y-2 sm:space-y-3">
                      {feature.benefits.map((benefit, benefitIndex) => (
                        <div
                          key={benefitIndex}
                          className="flex items-start text-xs sm:text-sm text-gray-700"
                        >
                          <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-emerald-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span className="leading-relaxed">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Additional Benefits Section */}
        <div className="mt-12 sm:mt-16 lg:mt-20">
          <div className="bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 shadow-lg">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center">
              <div>
                <h3 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-black mb-4 sm:mb-6 text-center lg:text-left">
                  Why Choose <span style={{ color: "#2d4d31" }}>Agritram?</span>
                </h3>
                <div className="space-y-3 sm:space-y-4">
                  <div className="flex items-start space-x-2 sm:space-x-3">
                    <div
                      className="w-2 h-2 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"
                      style={{ backgroundColor: "#2d4d31" }}
                    ></div>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                      <span className="font-bold">
                        Faster, Guaranteed Payments
                      </span>{" "}
                      Agritram settles trades in days not months—through smart
                      contract automation.
                    </p>
                  </div>
                  <div className="flex items-start space-x-2 sm:space-x-3">
                    <div
                      className="w-2 h-2 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"
                      style={{ backgroundColor: "#2d4d31" }}
                    ></div>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                      <span className="font-bold">
                        Traceability from Farm to Factory
                      </span>{" "}
                      Every tokenised lot comes with verifiable origin, quality,
                      and ownership records.
                    </p>
                  </div>
                  <div className="flex items-start space-x-2 sm:space-x-3">
                    <div
                      className="w-2 h-2 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"
                      style={{ backgroundColor: "#2d4d31" }}
                    ></div>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                      <span className="font-bold">
                        Built for Fairness and Inclusion
                      </span>{" "}
                      Farmers and traders keep more of what they earn with zero
                      hidden fees.
                    </p>
                  </div>
                  <div className="flex items-start space-x-2 sm:space-x-3">
                    <div
                      className="w-2 h-2 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"
                      style={{ backgroundColor: "#2d4d31" }}
                    ></div>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                      <span className="font-bold">Powered by Blockchain</span>{" "}
                      Every trade is transparent, rule-based, and free of
                      unnecessary intermediaries.
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3 sm:gap-6">
                <div
                  className="text-center p-3 sm:p-6 rounded-xl"
                  style={{ backgroundColor: "#fff5ea" }}
                >
                  <div className="text-xl sm:text-2xl md:text-3xl font-bold text-black mb-2">
                    600,000+
                  </div>
                  <div className="text-xs sm:text-sm md:text-base text-gray-600">
                    Farmers Onboarding via Verified Cooperatives
                  </div>
                </div>
                <div
                  className="text-center p-3 sm:p-6 rounded-xl"
                  style={{ backgroundColor: "#fff5ea" }}
                >
                  <div className="text-xl sm:text-2xl md:text-3xl font-bold text-black mb-2">
                    70%
                  </div>
                  <div className="text-xs sm:text-sm md:text-base text-gray-600">
                    Faster Settlement Time Than Traditional Trade
                  </div>
                </div>
                <div
                  className="text-center p-3 sm:p-6 rounded-xl"
                  style={{ backgroundColor: "#fff5ea" }}
                >
                  <div className="text-xl sm:text-2xl md:text-3xl font-bold text-black mb-2">
                    1M+ MT
                  </div>
                  <div className="text-xs sm:text-sm md:text-base text-gray-600">
                    Tokenised Commodity Potential
                  </div>
                </div>
                <div
                  className="text-center p-3 sm:p-6 rounded-xl"
                  style={{ backgroundColor: "#fff5ea" }}
                >
                  <div className="text-xl sm:text-2xl md:text-3xl font-bold text-black mb-2">
                    300+
                  </div>
                  <div className="text-xs sm:text-sm md:text-base text-gray-600">
                    Co-ops and Traders Ready for Integration
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
