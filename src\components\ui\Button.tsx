import React from "react";
import type { LucideIcon } from "lucide-react";

export type ButtonVariant = "primary" | "outline" | "ghost" | "secondary";
export type ButtonSize = "sm" | "md" | "lg";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: LucideIcon;
  iconPosition?: "left" | "right";
  children: React.ReactNode;
  isLoading?: boolean;
  fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  variant = "primary",
  size = "md",
  icon: Icon,
  iconPosition = "left",
  children,
  isLoading = false,
  fullWidth = false,
  className = "",
  disabled,
  ...props
}) => {
  // Base styles that apply to all buttons
  const baseStyles =
    "inline-flex items-center justify-center font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";

  // Variant styles
  const variantStyles = {
    // Primary button (solid background)
    primary:
      "bg-theme-button-bg text-theme-button-text hover:bg-theme-button-hover hover:scale-105 focus:ring-theme-button-bg",

    // Secondary button (solid background)
    secondary:
      "bg-theme-secondary-button-bg text-theme-secondary-button-text hover:bg-theme-secondary-button-hover hover:scale-105 focus:ring-theme-secondary-button-bg",

    // Outline button (transparent with border)
    outline:
      "bg-theme-transparent text-theme-button-bg border-2 border-theme-button-bg hover:bg-theme-button-hover hover:border-theme-button-hover hover:text-theme-button-text hover:scale-105 focus:ring-theme-button-bg",

    // Ghost button (text only with hover background)
    ghost:
      "text-theme-primary-text hover:text-theme-link hover:bg-theme-main-bg focus:ring-theme-link",
  };

  // Size styles
  const sizeStyles = {
    sm: "px-4 py-2 text-sm",
    md: "px-4 py-2",
    lg: "px-6 py-3 sm:px-8 sm:py-4",
  };

  // Icon size based on button size
  const iconSizes = {
    sm: "w-4 h-4",
    md: "w-5 h-5",
    lg: "w-5 h-5",
  };

  // Combine all styles
  const buttonClasses = [
    baseStyles,
    variantStyles[variant],
    sizeStyles[size],
    fullWidth ? "w-full" : "",
    className,
  ]
    .filter(Boolean)
    .join(" ");

  const iconClasses = [
    iconSizes[size],
    iconPosition === "left" ? "mr-2" : "ml-2",
    "group-hover:scale-110 transition-transform",
  ].join(" ");

  return (
    <button
      className={`${buttonClasses} group`}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <>
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current mr-2"></div>
          {children}
        </>
      ) : (
        <>
          {Icon && iconPosition === "left" && (
            <Icon className={iconClasses} aria-hidden="true" />
          )}
          {children}
          {Icon && iconPosition === "right" && (
            <Icon className={iconClasses} aria-hidden="true" />
          )}
        </>
      )}
    </button>
  );
};

export default Button;
