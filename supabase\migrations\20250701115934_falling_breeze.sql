/*
  # Remove visits_count from contact_messages table

  1. Changes
    - Remove `visits_count` column from `contact_messages` table
    - Each contact form submission creates a new record without visit tracking

  2. Rationale
    - Contact messages should be treated as individual inquiries
    - No need to track visit counts for separate message records
    - Simplifies the contact message data model
*/

-- Remove visits_count column from contact_messages table
ALTER TABLE contact_messages 
DROP COLUMN IF EXISTS visits_count;