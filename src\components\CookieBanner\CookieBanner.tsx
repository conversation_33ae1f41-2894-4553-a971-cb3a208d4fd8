import React, { useState, useEffect } from "react";
import {
  <PERSON>ting<PERSON>,
  <PERSON>,
  Bar<PERSON><PERSON>3,
  <PERSON>,
  Wrench,
  Clock,
} from "lucide-react";
import Button from "../ui/Button";
import {
  getCookieConsent,
  acceptAllCookies,
  rejectAllCookies,
  updateCookiePreferences,
  getCookieCategories,
  type CookieConsent,
} from "../../utils/cookies";
import { updateGTMConsent, trackConsentUpdate } from "../../utils/gtm";

interface CookieBannerProps {
  onConsentChange?: (consent: CookieConsent) => void;
  delayMs?: number; // Delay before showing banner
}

const CookieBanner: React.FC<CookieBannerProps> = ({
  onConsentChange,
  delayMs = 30000, // 30 seconds default delay
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [consent, setConsent] = useState<CookieConsent | null>(null);
  const [preferences, setPreferences] = useState({
    necessary: true,
    analytics: false,
    marketing: false,
    functional: false,
  });

  const [isDelayActive, setIsDelayActive] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [delayCountdown, setDelayCountdown] = useState(0);
  const [isReturningUser, setIsReturningUser] = useState(false);

  const cookieCategories = getCookieCategories();

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Add body padding when banner is visible to prevent content obstruction
  useEffect(() => {
    if (isVisible) {
      const bannerHeight = isMobile ? "120px" : "80px";
      document.body.style.paddingBottom = bannerHeight;
    } else {
      document.body.style.paddingBottom = "";
    }

    return () => {
      document.body.style.paddingBottom = "";
    };
  }, [isVisible, isMobile]);

  useEffect(() => {
    const checkConsentStatus = () => {
      const existingConsent = getCookieConsent();

      if (!existingConsent) {
        // No consent given yet - show banner after delay with countdown
        setDelayCountdown(Math.ceil(delayMs / 1000));

        const countdownInterval = setInterval(() => {
          setDelayCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(countdownInterval);
              setIsDelayActive(false);
              setIsVisible(true);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);

        return () => clearInterval(countdownInterval);
      } else {
        // Check if consent needs renewal (24 hours for rejected cookies)
        const consentAge = Date.now() - existingConsent.timestamp;
        const twentyFourHours = 24 * 60 * 60 * 1000;

        // If user rejected any optional cookies and it's been 24+ hours, show banner again
        const hasRejectedCookies =
          !existingConsent.analytics ||
          !existingConsent.marketing ||
          !existingConsent.functional;
        const needsRenewal =
          hasRejectedCookies && consentAge >= twentyFourHours;

        if (needsRenewal) {
          // Show banner again after 24 hours with countdown
          setIsReturningUser(true);
          setDelayCountdown(Math.ceil(delayMs / 1000));

          const countdownInterval = setInterval(() => {
            setDelayCountdown((prev) => {
              if (prev <= 1) {
                clearInterval(countdownInterval);
                setIsDelayActive(false);
                setIsVisible(true);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);

          return () => clearInterval(countdownInterval);
        } else {
          // Valid consent exists
          setConsent(existingConsent);
          setPreferences({
            necessary: existingConsent.necessary,
            analytics: existingConsent.analytics,
            marketing: existingConsent.marketing,
            functional: existingConsent.functional,
          });
        }
      }
    };

    checkConsentStatus();
  }, [delayMs]);

  const handleAcceptAll = () => {
    const newConsent = acceptAllCookies();
    setConsent(newConsent);
    setIsVisible(false);

    // Update GTM consent
    updateGTMConsent({
      analytics: true,
      marketing: true,
      functional: true,
    });

    // Track consent
    trackConsentUpdate({
      analytics: true,
      marketing: true,
      functional: true,
    });

    // Dispatch consent event for GTM loading
    window.dispatchEvent(
      new CustomEvent("cookieConsentUpdated", {
        detail: {
          analytics: true,
          marketing: true,
          functional: true,
        },
      })
    );

    onConsentChange?.(newConsent);
  };

  const handleRejectAll = () => {
    const newConsent = rejectAllCookies();
    setConsent(newConsent);
    setIsVisible(false);

    // Update GTM consent
    updateGTMConsent({
      analytics: false,
      marketing: false,
      functional: false,
    });

    // Track consent
    trackConsentUpdate({
      analytics: false,
      marketing: false,
      functional: false,
    });

    onConsentChange?.(newConsent);
  };

  const handleSavePreferences = () => {
    const newConsent = updateCookiePreferences(preferences);
    setConsent(newConsent);
    setIsVisible(false);
    setShowDetails(false);

    // Update GTM consent
    updateGTMConsent({
      analytics: preferences.analytics,
      marketing: preferences.marketing,
      functional: preferences.functional,
    });

    // Track consent
    trackConsentUpdate({
      analytics: preferences.analytics,
      marketing: preferences.marketing,
      functional: preferences.functional,
    });

    // Dispatch consent event for GTM loading
    window.dispatchEvent(
      new CustomEvent("cookieConsentUpdated", {
        detail: {
          analytics: preferences.analytics,
          marketing: preferences.marketing,
          functional: preferences.functional,
        },
      })
    );

    onConsentChange?.(newConsent);
  };

  const handlePreferenceChange = (category: string, value: boolean) => {
    if (category === "necessary") return; // Can't disable necessary cookies
    setPreferences((prev) => ({
      ...prev,
      [category]: value,
    }));
  };

  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case "necessary":
        return Shield;
      case "analytics":
        return BarChart3;
      case "marketing":
        return Target;
      case "functional":
        return Wrench;
      default:
        return Shield;
    }
  };

  // Show countdown indicator when delay is active
  if (!isVisible) {
    if (isDelayActive && delayCountdown > 0 && delayCountdown <= 10) {
      return (
        <div className="fixed bottom-4 right-4 z-30 bg-white rounded-lg shadow-medium border border-neutral-200 p-3 animate-pulse">
          <div className="flex items-center space-x-2 text-xs text-neutral-600">
            <Clock className="w-3 h-3" />
            <span>Cookie banner in {delayCountdown}s</span>
          </div>
        </div>
      );
    }
    // Don't show banner if consent already exists
    if (consent) {
      return null;
    }
    return null;
  }

  return (
    <>
      {/* Non-obstructive banner - positioned at bottom without overlay */}
      <div
        className={`fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-neutral-200 shadow-strong ${
          isMobile ? "pb-safe" : ""
        }`}
      >
        <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8">
          {!showDetails ? (
            // Compact banner view - optimized for mobile
            <div className={`${isMobile ? "py-4" : "py-3 sm:py-4"}`}>
              <div
                className={`flex ${isMobile ? "flex-col gap-4" : "flex-col sm:flex-row sm:items-center sm:justify-between gap-3"}`}
              >
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start space-x-3">
                    <Shield
                      className={`${isMobile ? "w-4 h-4" : "w-5 h-5"} text-theme-button-bg mt-0.5 flex-shrink-0`}
                    />
                    <div className="min-w-0 flex-1">
                      <p
                        className={`${isMobile ? "text-xs" : "text-sm"} text-theme-primary-text leading-relaxed`}
                      >
                        {isReturningUser ? (
                          // Special message for returning users
                          <>
                            {isMobile ? (
                              <>
                                <span className="font-medium text-amber-600">
                                  Welcome back!
                                </span>{" "}
                                Would you like to reconsider your cookie
                                preferences?{" "}
                                <a
                                  href="/privacy"
                                  className="text-theme-link hover:text-theme-link-hover underline"
                                >
                                  Learn more
                                </a>
                              </>
                            ) : (
                              <>
                                <span className="font-medium text-amber-600">
                                  Welcome back!
                                </span>{" "}
                                It's been 24 hours since you last visited. Would
                                you like to reconsider your cookie preferences?{" "}
                                <a
                                  href="/privacy"
                                  className="text-theme-link hover:text-theme-link-hover underline ml-1"
                                >
                                  Privacy Policy
                                </a>
                              </>
                            )}
                          </>
                        ) : (
                          // Standard message for new users
                          <>
                            {isMobile ? (
                              <>
                                We use cookies to enhance your experience.{" "}
                                <a
                                  href="/privacy"
                                  className="text-theme-link hover:text-theme-link-hover underline"
                                >
                                  Learn more
                                </a>
                              </>
                            ) : (
                              <>
                                We use cookies to enhance your experience.{" "}
                                <a
                                  href="/privacy"
                                  className="text-theme-link hover:text-theme-link-hover underline ml-1"
                                >
                                  Privacy Policy
                                </a>
                                {isDelayActive && delayCountdown > 0 && (
                                  <span className="inline-flex items-center ml-2 text-xs text-neutral-500">
                                    <Clock className="w-3 h-3 mr-1" />
                                    Appearing in {delayCountdown}s
                                  </span>
                                )}
                              </>
                            )}
                          </>
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Actions - Mobile optimized */}
                <div
                  className={`flex ${
                    isMobile
                      ? "flex-row gap-2"
                      : "flex-col sm:flex-row gap-2 sm:gap-3"
                  } flex-shrink-0`}
                >
                  {!isMobile && (
                    <Button
                      variant="ghost"
                      size="sm"
                      icon={Settings}
                      onClick={() => setShowDetails(true)}
                      className="text-xs"
                    >
                      Customize
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size={isMobile ? "sm" : "sm"}
                    onClick={handleRejectAll}
                    className={`${isMobile ? "text-xs flex-1" : "text-xs"}`}
                  >
                    {isMobile ? "Reject" : "Reject All"}
                  </Button>
                  <Button
                    variant="primary"
                    size={isMobile ? "sm" : "sm"}
                    onClick={handleAcceptAll}
                    className={`${isMobile ? "text-xs flex-1" : "text-xs"}`}
                  >
                    {isMobile ? "Accept" : "Accept All"}
                  </Button>
                  {isMobile && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowDetails(true)}
                      className="text-xs px-2 flex items-center justify-center"
                      aria-label="Cookie Settings"
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ) : (
            // Detailed preferences view
            <div
              className={`${isMobile ? "py-3" : "py-4 sm:py-6"} ${isMobile ? "max-h-[85vh]" : "max-h-96"} overflow-y-auto`}
            >
              <div className="flex items-center justify-between mb-3 sm:mb-4">
                <h3
                  className={`${isMobile ? "text-base" : "text-lg"} font-semibold text-theme-primary-text`}
                >
                  Cookie Preferences
                </h3>
              </div>
              <p
                className={`${isMobile ? "text-xs" : "text-sm"} text-neutral-600 mb-3 sm:mb-4`}
              >
                Choose which cookies you want to allow. You can change these
                settings at any time.
              </p>

              <div
                className={`grid gap-2 sm:gap-3 ${isMobile ? "" : "max-h-48 overflow-y-auto"}`}
              >
                {cookieCategories.map((category) => {
                  const Icon = getCategoryIcon(category.id);
                  const isChecked =
                    preferences[category.id as keyof typeof preferences];

                  return (
                    <div
                      key={category.id}
                      className={`flex items-center justify-between ${isMobile ? "p-3" : "p-3"} border border-neutral-200 rounded-lg hover:border-neutral-300 transition-colors ${isMobile ? "bg-neutral-50/50" : ""}`}
                    >
                      <div
                        className={`flex items-center ${isMobile ? "space-x-3" : "space-x-3"} flex-1`}
                      >
                        <Icon
                          className={`${isMobile ? "w-4 h-4" : "w-4 h-4"} text-theme-button-bg flex-shrink-0`}
                        />
                        <div className="flex-1 min-w-0">
                          <h4
                            className={`${isMobile ? "text-sm" : "text-sm"} font-medium text-theme-primary-text`}
                          >
                            {category.name}
                          </h4>
                          <p
                            className={`${isMobile ? "text-xs leading-relaxed" : "text-xs"} text-neutral-600 ${isMobile ? "overflow-hidden" : "truncate"}`}
                            style={
                              isMobile
                                ? {
                                    display: "-webkit-box",
                                    WebkitLineClamp: 3,
                                    WebkitBoxOrient: "vertical" as const,
                                    overflow: "hidden",
                                  }
                                : undefined
                            }
                          >
                            {category.description}
                          </p>
                        </div>
                      </div>
                      <label
                        className={`relative inline-flex items-center cursor-pointer ${isMobile ? "ml-2" : "ml-3"}`}
                      >
                        <input
                          type="checkbox"
                          checked={isChecked}
                          disabled={category.required}
                          onChange={(e) =>
                            handlePreferenceChange(
                              category.id,
                              e.target.checked
                            )
                          }
                          className="sr-only peer"
                        />
                        <div
                          className={`relative ${isMobile ? "w-8 h-4" : "w-9 h-5"} rounded-full transition-colors ${
                            isChecked ? "bg-theme-button-bg" : "bg-neutral-300"
                          } ${category.required ? "opacity-50 cursor-not-allowed" : ""}`}
                        >
                          <div
                            className={`absolute top-[2px] left-[2px] bg-white ${isMobile ? "w-3 h-3" : "w-4 h-4"} rounded-full transition-transform ${
                              isChecked
                                ? isMobile
                                  ? "translate-x-3"
                                  : "translate-x-4"
                                : "translate-x-0"
                            }`}
                          />
                        </div>
                      </label>
                    </div>
                  );
                })}
              </div>

              <div
                className={`flex ${isMobile ? "flex-col gap-3" : "flex-col sm:flex-row gap-2"} ${isMobile ? "pt-4 mt-2" : "pt-3"} border-t border-neutral-200`}
              >
                <Button
                  variant="primary"
                  size={isMobile ? "md" : "sm"}
                  onClick={handleSavePreferences}
                  className={`${isMobile ? "text-sm font-medium" : ""} flex-1 sm:flex-none`}
                >
                  Save Preferences
                </Button>
                <div className={`flex ${isMobile ? "gap-3" : "gap-2"}`}>
                  <Button
                    variant="outline"
                    size={isMobile ? "md" : "sm"}
                    onClick={handleAcceptAll}
                    className={`${isMobile ? "text-sm flex-1" : ""} sm:flex-none`}
                  >
                    Accept All
                  </Button>
                  <Button
                    variant="ghost"
                    size={isMobile ? "md" : "sm"}
                    onClick={() => setShowDetails(false)}
                    className={`${isMobile ? "text-sm flex-1" : ""} sm:flex-none`}
                  >
                    Back
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default CookieBanner;
