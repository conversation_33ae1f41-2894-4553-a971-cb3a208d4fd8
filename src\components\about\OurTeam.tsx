import React from "react";
import "./OurTeam.css";

interface TeamMember {
  id: number;
  name: string;
  position: string;
  bio: string;
  image: string;
  linkedin?: string;
}

const AboutUs: React.FC = () => {
  const teamMembers: TeamMember[] = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON>",
      position: "CEO & Co-Founder",
      bio: "Strategic leader driving innovation, growth, and success through data-led marketing and operational excellence.",
      image: "https://www.luminvibe.co.uk/images/agritram/sharvil.png",
      linkedin: "https://www.linkedin.com/in/sharvil-sreenivas-635715157/",
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON><PERSON>",
      position: "<PERSON><PERSON> & Co-Founder",
      bio: "Cybersecurity and Blockchain expert and tech strategist focused on building secure, scalable systems and driving digital transformation.",
      image: "https://www.luminvibe.co.uk/images/agritram/shashank.png",
      linkedin: "https://www.linkedin.com/in/shashank-s-445769a7/",
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "<PERSON>O & Co-Founder",
      bio: "Product innovator combining mobile/web development, cybersecurity, and blockchain technologies to deliver secure, user-centric digital experiences.",
      image: "https://www.luminvibe.co.uk/images/agritram/mohammad.png",
      linkedin: "https://www.linkedin.com/in/g-mohammad-rafi/",
    },
    {
      id: 4,
      name: "Vigneshwar Balaji",
      position: "CMO & Co-Founder",
      bio: "Creative visionary crafting engaging visual content that fuses storytelling, technology, and brand strategy.",
      image: "https://www.luminvibe.co.uk/images/agritram/vigneshwar.png",
      linkedin: "https://www.linkedin.com/in/vigneshwar-p-3945431b4/",
    },
    {
      id: 5,
      name: "Natalia Micu",
      position: "CFO",
      bio: "Financial strategist with deep expertise in budgeting, forecasting, and driving sustainable growth through data-driven decision-making.",
      image: "https://www.luminvibe.co.uk/images/agritram/natalia.png",
      linkedin: "https://www.linkedin.com/in/natalia-m-accounts75/",
    },
  ];

  return (
    <div id="team" className="pt-12 sm:pt-16 lg:pt-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-16 lg:mb-20">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-black mb-4 sm:mb-6 bg-gradient-to-r from-[#2d4d31] to-[#1f3322] bg-clip-text text-transparent">
            Meet the Team
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-black max-w-3xl mx-auto leading-relaxed px-2">
            Our diverse team of agricultural experts, engineers, and data
            scientists is passionate about transforming farming through
            innovative technology solutions.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10 max-w-6xl mx-auto mb-12 sm:mb-16 lg:mb-20">
          {teamMembers.map((member) => (
            <div
              key={member.id}
              className="bg-gradient-to-br from-white to-[#fff5ea] rounded-xl sm:rounded-2xl p-6 sm:p-8 lg:p-10 text-center flex flex-col items-center shadow-lg hover:shadow-xl border border-[#2d4d31]/10 hover:border-[#2d4d31]/30 transition-all duration-300 hover:-translate-y-2 relative overflow-hidden group"
            >
              {/* Top gradient bar */}
              <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-[#2d4d31] via-[#d36c3c] to-[#2d4d31] rounded-t-xl sm:rounded-t-2xl"></div>

              <div className="relative w-28 h-28 sm:w-32 sm:h-32 lg:w-36 lg:h-36 mb-4 sm:mb-6 rounded-full overflow-hidden shadow-lg border-4 border-[#2d4d31]/10 flex-shrink-0">
                <img
                  src={member.image}
                  alt={member.name}
                  loading="lazy"
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
                {member.linkedin && (
                  <a
                    href={member.linkedin}
                    className="absolute bottom-2 right-2 w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#d36c3c] to-[#b85a32] text-white rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:bg-gradient-to-br hover:from-[#2d4d31] hover:to-[#1f3322] shadow-lg"
                    aria-label={`${member.name} LinkedIn profile`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 20 20"
                      fill="none"
                      className="sm:w-5 sm:h-5"
                    >
                      <path
                        d="M16.25 2.5H3.75C3.06 2.5 2.5 3.06 2.5 3.75V16.25C2.5 16.94 3.06 17.5 3.75 17.5H16.25C16.94 17.5 17.5 16.94 17.5 16.25V3.75C17.5 3.06 16.94 2.5 16.25 2.5ZM7.5 15H5V8.75H7.5V15ZM6.25 7.69C5.56 7.69 5 7.13 5 6.44C5 5.75 5.56 5.19 6.25 5.19C6.94 5.19 7.5 5.75 7.5 6.44C7.5 7.13 6.94 7.69 6.25 7.69ZM15 15H12.5V11.94C12.5 11.06 12.5 9.94 11.25 9.94C10 9.94 9.81 10.88 9.81 11.88V15H7.31V8.75H9.69V9.81H9.72C10.06 9.19 10.81 8.56 11.94 8.56C14.44 8.56 15 10.19 15 12.31V15Z"
                        fill="currentColor"
                      />
                    </svg>
                  </a>
                )}
              </div>

              <div className="flex flex-col items-center text-center flex-grow">
                <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-black mb-2 leading-tight">
                  {member.name}
                </h3>
                <p className="text-xs sm:text-sm font-semibold text-[#d36c3c] mb-3 sm:mb-4 uppercase tracking-wide">
                  {member.position}
                </p>
                <p className="text-sm sm:text-base text-black leading-relaxed">
                  {member.bio}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AboutUs;
