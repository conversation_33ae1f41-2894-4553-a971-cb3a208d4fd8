import React from "react";
import { Mail, MapPin, Linkedin, Youtube } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";

const Footer: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleNavigation = (href: string) => {
    // Define which sections are on which pages
    const homePageSections = [
      "#features",
      "#video",
      "#whitepaper",
      "#coming-soon",
      "#early-access",
      "#partner",
      "#contact",
      "#about",
    ];
    const aboutPageSections = ["#team", "#advisory"];

    if (homePageSections.includes(href)) {
      // Navigate to home page and scroll to section
      if (location.pathname !== "/") {
        navigate("/", { state: { scrollTo: href } });
      } else {
        const element = document.querySelector(href);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }
    } else if (aboutPageSections.includes(href)) {
      // Navigate to about page and scroll to section
      if (location.pathname !== "/about") {
        navigate("/about", { state: { scrollTo: href } });
      } else {
        const element = document.querySelector(href);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }
    }
  };

  const footerLinks = {
    product: [
      { label: "Platform Features", href: "#features" },
      { label: "White Paper", href: "#whitepaper" },
      { label: "Video Demo", href: "#video" },
      { label: "Coming Soon", href: "#coming-soon" },
      { label: "Early Access", href: "#early-access" },
    ],
    company: [
      { label: "About Agritram", href: "#about" },
      { label: "Our Team", href: "#team" },
      { label: "Advisory Board", href: "#advisory" },
      { label: "Contact Us", href: "#contact" },
    ],
    partnership: [
      { label: "Partner With Us", href: "#partner" },
      { label: "For Corporates", href: "#partner" },
      { label: "For Investors", href: "#partner" },
      { label: "For Angel Investor", href: "#partner" },
      { label: "For CIB", href: "#partner" },
    ],
    resources: [
      { label: "Whitepaper Download", href: "#whitepaper" },
      { label: "Platform Demo", href: "#video" },
      { label: "Get Early Access", href: "#early-access" },
      { label: "Contact Support", href: "#contact" },
    ],
  };

  const socialLinks = [
    {
      icon: Linkedin,
      href: "https://www.linkedin.com/company/agritram/",
      label: "LinkedIn",
    },
    {
      icon: Youtube,
      href: "https://www.youtube.com/@Agritram",
      label: "Visit Us",
    },
    {
      icon: Mail,
      href: "mailto:<EMAIL>",
      label: "Email Us",
    },
  ];

  return (
    <footer className="bg-theme-main-bg text-gray-900 relative overflow-hidden pb-20 sm:pb-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 sm:py-16">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-6 sm:gap-8">
            {/* Brand Section */}
            <div className="sm:col-span-2 lg:col-span-2">
              <div className="mb-4 sm:mb-6">
                {/* Company and Project Logos */}
                <div className="flex items-center justify-center sm:justify-start space-x-2 sm:space-x-3 mb-3 sm:mb-4">
                  <img
                    src="https://www.luminvibe.co.uk/images/favicon.ico"
                    alt="LuminVibe"
                    className="h-8 w-8 sm:h-10 sm:w-10 object-contain"
                  />
                  <div className="w-0.5 h-8 sm:h-10 bg-gray-300 mx-1 sm:mx-2"></div>
                  <div className="flex flex-col">
                    <p className="text-xs text-gray-500 mt-1 font-medium">
                      A LuminVibe Project
                    </p>
                    <img
                      src="https://www.luminvibe.co.uk/images/agritram/logo.png"
                      alt="Agritram"
                      className="h-6 sm:h-8 object-contain"
                    />
                  </div>
                </div>
              </div>
              <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 leading-relaxed text-center sm:text-left px-2 sm:px-0">
                Revolutionizing agricultural finance through blockchain
                transparency. Connecting farmers, traders, and manufacturers in
                a trusted ecosystem.
              </p>
              <div className="space-y-2 sm:space-y-3">
                <div className="flex items-center justify-center sm:justify-start text-gray-600">
                  <Mail className="h-3 w-3 sm:h-4 sm:w-4 mr-2 sm:mr-3 text-emerald-500 flex-shrink-0" />
                  <span className="text-xs sm:text-sm break-all">
                    <EMAIL>
                  </span>
                </div>
                <div className="flex items-center justify-center sm:justify-start text-gray-600">
                  <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-2 sm:mr-3 text-emerald-500 flex-shrink-0" />
                  <span className="text-xs sm:text-sm">
                    London, United Kingdom
                  </span>
                </div>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="text-left">
              <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-3 sm:mb-4">
                Product
              </h3>
              <ul className="space-y-1 sm:space-y-2">
                {footerLinks.product.map((link, index) => (
                  <li key={index}>
                    <button
                      onClick={() => handleNavigation(link.href)}
                      className="text-xs sm:text-sm text-gray-600 hover:text-emerald-500 transition-colors duration-200 block"
                    >
                      {link.label}
                    </button>
                  </li>
                ))}
              </ul>
            </div>

            <div className="text-left">
              <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-3 sm:mb-4">
                Company
              </h3>
              <ul className="space-y-1 sm:space-y-2">
                {footerLinks.company.map((link, index) => (
                  <li key={index}>
                    <button
                      onClick={() => handleNavigation(link.href)}
                      className="text-xs sm:text-sm text-gray-600 hover:text-emerald-500 transition-colors duration-200 block"
                    >
                      {link.label}
                    </button>
                  </li>
                ))}
              </ul>
            </div>

            <div className="text-left">
              <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-3 sm:mb-4">
                Partnership
              </h3>
              <ul className="space-y-1 sm:space-y-2">
                {footerLinks.partnership.map((link, index) => (
                  <li key={index}>
                    <button
                      onClick={() => handleNavigation(link.href)}
                      className="text-xs sm:text-sm text-gray-600 hover:text-emerald-500 transition-colors duration-200 block"
                    >
                      {link.label}
                    </button>
                  </li>
                ))}
              </ul>
            </div>

            <div className="text-left">
              <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-3 sm:mb-4">
                Resources
              </h3>
              <ul className="space-y-1 sm:space-y-2">
                {footerLinks.resources.map((link, index) => (
                  <li key={index}>
                    <button
                      onClick={() => handleNavigation(link.href)}
                      className="text-xs sm:text-sm text-gray-600 hover:text-emerald-500 transition-colors duration-200 block"
                    >
                      {link.label}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-emerald-600 py-6 sm:py-8">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            {/* Copyright */}
            <div className="text-center sm:text-left order-2 sm:order-1">
              <p className="text-sm sm:text-base font-medium">
                Copyright © {new Date().getFullYear()}{" "}
                <span className="font-semibold">LuminVibe & Agritram </span>
                <span className="text-gray-600 mt-1">
                  - All rights reserved
                </span>
              </p>
              {/* Legal Links */}
              <div className="flex flex-wrap justify-center sm:justify-start gap-3 sm:gap-6 mt-2 sm:mt-1">
                <button
                  onClick={() => navigate("/terms")}
                  className="text-xs sm:text-sm text-gray-500 hover:text-emerald-500 transition-colors duration-200"
                >
                  Terms of Service
                </button>
                <button
                  onClick={() => navigate("/privacy")}
                  className="text-xs sm:text-sm text-gray-500 hover:text-emerald-500 transition-colors duration-200"
                >
                  Privacy Policy
                </button>
                <button
                  onClick={() => navigate("/cookies")}
                  className="text-xs sm:text-sm text-gray-500 hover:text-emerald-500 transition-colors duration-200"
                >
                  Cookie Policy
                </button>
                <button
                  onClick={() => navigate("/disclaimer")}
                  className="text-xs sm:text-sm text-gray-500 hover:text-emerald-500 transition-colors duration-200"
                >
                  Disclaimer
                </button>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-3 sm:space-x-4 order-1 sm:order-2">
              {socialLinks.map((social, index) => {
                const IconComponent = social.icon;
                return (
                  <a
                    key={index}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-gray-200 p-2 rounded-lg hover:bg-emerald-600 transition-colors duration-200 group"
                    aria-label={social.label}
                  >
                    <IconComponent className="h-4 w-4 sm:h-5 sm:w-5 text-gray-600 group-hover:text-white" />
                  </a>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      {/* Absolutely positioned brand text overlay */}
      <div className="absolute left-1/2 bottom-0 -translate-x-1/2 z-0 w-full flex justify-center pointer-events-none select-none">
        <span
          className="font-extrabold font-[Poppins,sans-serif] text-[200px] leading-[.8] text-emerald-600/20 m-0 block translate-y-[37px] tracking-tight
            max-[1200px]:text-[150px] max-[1200px]:translate-y-[30px]
            max-[992px]:text-[120px] max-[992px]:translate-y-[25px]
            max-[768px]:text-[100px] max-[768px]:translate-y-[25px]
            max-[558px]:text-[80px] max-[558px]:translate-y-[25px]
            max-[430px]:text-[60px] max-[430px]:translate-y-[22px]
            max-[326px]:text-[40px] max-[326px]:translate-y-[22px]"
          style={{ fontFamily: "Poppins, sans-serif" }}
        >
          AGRITRAM
        </span>
      </div>
    </footer>
  );
};

export default Footer;
