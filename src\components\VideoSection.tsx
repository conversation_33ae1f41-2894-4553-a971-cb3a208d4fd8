import { Play } from "lucide-react";
import React, { useState } from "react";

const YOUTUBE_EMBED_URL =
  "https://www.youtube.com/embed/EAnpw5oalc0?si=NpVS4_MbGf1UAGsK";

const VideoSection: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handleClose = () => {
    setIsPlaying(false);
  };

  return (
    <section
      id="video"
      className="py-12 sm:py-16 lg:py-24 relative overflow-hidden"
      style={{
        backgroundImage: `linear-gradient(rgba(45, 77, 49, 0.5), rgba(45, 77, 49, 0.5)), url('https://www.luminvibe.co.uk/images/agritram/1.png')`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundAttachment: window.innerWidth > 768 ? "fixed" : "scroll",
      }}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center text-center">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 drop-shadow-lg px-2">
            See Agritram in Action
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-white/90 mb-8 sm:mb-10 max-w-2xl drop-shadow px-2">
            A closer look at how we connect sellers and buyers through
            tokenised, trusted trade.
          </p>
          {/* Video Player */}
          <div className="w-full max-w-4xl mb-12 sm:mb-16 px-2 sm:px-0">
            <div className="relative aspect-video bg-black rounded-2xl sm:rounded-3xl overflow-hidden shadow-2xl border-2 sm:border-4 border-white/10">
              {/* Show background image if not playing */}
              {!isPlaying && (
                <img
                  src="https://www.luminvibe.co.uk/images/agritram/video-bg.png"
                  alt="Agritram video background"
                  className="absolute inset-0 w-full h-full object-cover z-0"
                  style={{ borderRadius: "1.5rem" }}
                />
              )}
              {!isPlaying ? (
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center z-10">
                  <button
                    className="group w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-white/30 hover:bg-white/50 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg"
                    onClick={handlePlay}
                    aria-label="Play video"
                  >
                    <Play
                      className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 text-white group-hover:scale-110 transition-transform"
                      fill="currentColor"
                    />
                  </button>
                </div>
              ) : (
                <>
                  <iframe
                    src={YOUTUBE_EMBED_URL + "&autoplay=1"}
                    title="YouTube video player"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    referrerPolicy="strict-origin-when-cross-origin"
                    allowFullScreen
                    className="absolute inset-0 w-full h-full object-cover z-20 border-0"
                    style={{
                      borderRadius: window.innerWidth > 640 ? "1.5rem" : "1rem",
                    }}
                  />
                  <button
                    className="absolute top-2 right-2 sm:top-4 sm:right-4 z-30 bg-black/60 text-white rounded-full px-2 py-1 sm:px-3 sm:py-1 text-xs sm:text-sm hover:bg-black/80 transition"
                    onClick={handleClose}
                  >
                    Close
                  </button>
                </>
              )}
              {/* Video Overlay Text */}
              {!isPlaying && (
                <div className="absolute bottom-3 left-3 sm:bottom-6 sm:left-6 text-white drop-shadow z-20 pointer-events-none">
                  <h3 className="text-lg sm:text-xl lg:text-2xl text-white font-semibold mb-1">
                    Agritram: The Future of Agriculture
                  </h3>
                  <p className="opacity-80 text-white text-sm sm:text-base">
                    Connecting ecosystems, empowering farmers
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Disclaimer */}
          <div className="w-full max-w-4xl mb-8 sm:mb-12 px-2 sm:px-0">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-4 sm:p-6 text-center">
              <p className="text-white/70 text-xs sm:text-sm leading-relaxed">
                <span className="font-semibold text-white/90">Disclaimer:</span>
                <br className="sm:hidden" />
                <span className="sm:ml-2">
                  The visuals and processes shown in this video represent a
                  prototype version of Agritram. Features, functions, and user
                  experiences may evolve as the platform moves through its
                  development stages.
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default VideoSection;
