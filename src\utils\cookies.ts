import Cookies from 'js-cookie';

// <PERSON><PERSON> names
export const COOKIE_CONSENT_NAME = 'agritram_cookie_consent';
export const COOKIE_PREFERENCES_NAME = 'agritram_cookie_preferences';

// Cookie consent status
export interface CookieConsent {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
  timestamp: number;
  version: string;
}

// Default consent (only necessary cookies)
export const DEFAULT_CONSENT: CookieConsent = {
  necessary: true,
  analytics: false,
  marketing: false,
  functional: false,
  timestamp: Date.now(),
  version: '1.0',
};

// Get cookie consent status
export const getCookieConsent = (): CookieConsent | null => {
  try {
    const consent = Cookies.get(COOKIE_CONSENT_NAME);
    if (consent) {
      return JSON.parse(consent);
    }
    return null;
  } catch (error) {
    console.error('Error parsing cookie consent:', error);
    return null;
  }
};

// Set cookie consent
export const setCookieConsent = (consent: Partial<CookieConsent>): void => {
  const fullConsent: CookieConsent = {
    ...DEFAULT_CONSENT,
    ...consent,
    timestamp: Date.now(),
  };

  // Set cookie for 1 year
  Cookies.set(COOKIE_CONSENT_NAME, JSON.stringify(fullConsent), {
    expires: 365,
    secure: true,
    sameSite: 'strict',
  });
};

// Check if user has given consent
export const hasGivenConsent = (): boolean => {
  return getCookieConsent() !== null;
};

// Check if specific cookie type is allowed
export const isCookieTypeAllowed = (type: keyof Omit<CookieConsent, 'timestamp' | 'version'>): boolean => {
  const consent = getCookieConsent();
  if (!consent) return type === 'necessary'; // Only necessary cookies by default
  return consent[type];
};

// Accept all cookies
export const acceptAllCookies = (): CookieConsent => {
  const consent: CookieConsent = {
    necessary: true,
    analytics: true,
    marketing: true,
    functional: true,
    timestamp: Date.now(),
    version: '1.0',
  };
  setCookieConsent(consent);
  return consent;
};

// Reject all non-necessary cookies
export const rejectAllCookies = (): CookieConsent => {
  const consent: CookieConsent = {
    necessary: true,
    analytics: false,
    marketing: false,
    functional: false,
    timestamp: Date.now(),
    version: '1.0',
  };
  setCookieConsent(consent);
  return consent;
};

// Clear all cookies (except necessary ones)
export const clearNonNecessaryCookies = (): void => {
  const consent = getCookieConsent();
  if (!consent) return;

  // List of cookies to clear based on consent
  const cookiesToClear = [];

  if (!consent.analytics) {
    cookiesToClear.push('_ga', '_ga_*', '_gid', '_gat', '_gtag_*');
  }

  if (!consent.marketing) {
    cookiesToClear.push('_fbp', '_fbc', 'fr', 'tr');
  }

  if (!consent.functional) {
    cookiesToClear.push('_hjid', '_hjFirstSeen', '_hjIncludedInSessionSample');
  }

  // Clear cookies
  cookiesToClear.forEach(cookieName => {
    if (cookieName.includes('*')) {
      // Handle wildcard cookies
      const prefix = cookieName.replace('*', '');
      Object.keys(document.cookie.split(';')).forEach(key => {
        if (key.trim().startsWith(prefix)) {
          Cookies.remove(key.trim());
        }
      });
    } else {
      Cookies.remove(cookieName);
    }
  });
};

// Update cookie preferences
export const updateCookiePreferences = (preferences: Partial<CookieConsent>): CookieConsent => {
  const currentConsent = getCookieConsent() || DEFAULT_CONSENT;
  const updatedConsent: CookieConsent = {
    ...currentConsent,
    ...preferences,
    timestamp: Date.now(),
  };
  
  setCookieConsent(updatedConsent);
  
  // Clear cookies that are no longer allowed
  clearNonNecessaryCookies();
  
  return updatedConsent;
};

// Check if consent needs renewal (older than 1 year)
export const needsConsentRenewal = (): boolean => {
  const consent = getCookieConsent();
  if (!consent) return true;
  
  const oneYearAgo = Date.now() - (365 * 24 * 60 * 60 * 1000);
  return consent.timestamp < oneYearAgo;
};

// Get cookie categories for display
export const getCookieCategories = () => {
  return [
    {
      id: 'necessary',
      name: 'Necessary Cookies',
      description: 'These cookies are essential for the website to function properly. They cannot be disabled.',
      required: true,
      cookies: ['Session cookies', 'Security cookies', 'Load balancing cookies'],
    },
    {
      id: 'analytics',
      name: 'Analytics Cookies',
      description: 'These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.',
      required: false,
      cookies: ['Google Analytics', 'Performance tracking'],
    },
    {
      id: 'marketing',
      name: 'Marketing Cookies',
      description: 'These cookies are used to track visitors across websites to display relevant advertisements.',
      required: false,
      cookies: ['Google Ads', 'Facebook Pixel', 'Retargeting pixels'],
    },
    {
      id: 'functional',
      name: 'Functional Cookies',
      description: 'These cookies enable enhanced functionality and personalization, such as videos and live chats.',
      required: false,
      cookies: ['Video players', 'Chat widgets', 'Social media embeds'],
    },
  ];
};
