/*
  # Create whitepaper leads table

  1. New Tables
    - `whitepaper_leads`
      - `id` (uuid, primary key)
      - `full_name` (text, required)
      - `email` (text, unique, required)
      - `company` (text, required)
      - `visits_count` (integer, default 1)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `whitepaper_leads` table
    - Add policy for service role access (needed for edge function)

  3. Additional Features
    - Index on email for faster lookups
    - Tri<PERSON> to automatically update `updated_at` timestamp
*/

-- Create the whitepaper_leads table
CREATE TABLE IF NOT EXISTS whitepaper_leads (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  full_name text NOT NULL,
  email text UNIQUE NOT NULL,
  company text NOT NULL,
  visits_count integer DEFAULT 1,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE whitepaper_leads ENABLE ROW LEVEL SECURITY;

-- Create policy for service role access (needed for edge functions)
CREATE POLICY "Service role can manage whitepaper leads"
  ON whitepaper_leads
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Add index for faster email lookups
CREATE INDEX IF NOT EXISTS idx_whitepaper_leads_email ON whitepaper_leads (email);

-- Create function to update 'updated_at' automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to call the function on update
DROP TRIGGER IF EXISTS update_whitepaper_leads_updated_at ON whitepaper_leads;
CREATE TRIGGER update_whitepaper_leads_updated_at
  BEFORE UPDATE ON whitepaper_leads
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();