/*
# Add visits_count to early_access_leads table

1. Schema Changes
   - Add `visits_count` column to `early_access_leads` table
   - Set default value to 1 for new records
   - Update existing records to have visits_count = 1

2. Purpose
   - Track how many times a user has requested early access
   - Similar functionality to whitepaper_leads table
   - Helps with analytics and user engagement tracking
*/

-- Add visits_count column to early_access_leads table
ALTER TABLE early_access_leads 
ADD COLUMN IF NOT EXISTS visits_count integer DEFAULT 1;

-- Update existing records to have visits_count = 1
UPDATE early_access_leads 
SET visits_count = 1 
WHERE visits_count IS NULL;