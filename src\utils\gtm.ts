// Google Tag Manager utility functions
declare global {
  interface Window {
    dataLayer: Record<string, unknown>[];
    gtag: (
      ...args: [string, string | Record<string, unknown>, ...unknown[]]
    ) => void;
    google_tag_manager?: Record<string, unknown>;
  }
}

export const GTM_ID = "GTM-58F4LHM6";

// Initialize dataLayer if it doesn't exist
export const initializeDataLayer = () => {
  window.dataLayer = window.dataLayer || [];
};

// Push events to dataLayer
export const gtmPush = (data: Record<string, unknown>) => {
  if (typeof window !== "undefined" && window.dataLayer) {
    window.dataLayer.push(data);
  }
};

// Track page views
export const trackPageView = (url: string, title?: string) => {
  gtmPush({
    event: "page_view",
    page_location: url,
    page_title: title || document.title,
  });
};

// Track custom events
export const trackEvent = (
  eventName: string,
  parameters?: Record<string, unknown>
) => {
  gtmPush({
    event: eventName,
    ...parameters,
  });
};

// Track form submissions
export const trackFormSubmission = (
  formName: string,
  formData?: Record<string, unknown>
) => {
  gtmPush({
    event: "form_submit",
    form_name: formName,
    ...formData,
  });
};

// Track file downloads
export const trackDownload = (
  fileName: string,
  fileType: string,
  downloadUrl: string
) => {
  gtmPush({
    event: "file_download",
    file_name: fileName,
    file_type: fileType,
    download_url: downloadUrl,
  });
};

// Track button clicks
export const trackButtonClick = (
  buttonName: string,
  buttonLocation?: string
) => {
  gtmPush({
    event: "button_click",
    button_name: buttonName,
    button_location: buttonLocation,
  });
};

// Track video interactions
export const trackVideoEvent = (
  action: string,
  videoTitle: string,
  videoUrl?: string
) => {
  gtmPush({
    event: "video_" + action,
    video_title: videoTitle,
    video_url: videoUrl,
  });
};

// Set user properties
export const setUserProperties = (properties: Record<string, unknown>) => {
  gtmPush({
    event: "user_properties",
    ...properties,
  });
};

// Track consent status
export const trackConsentUpdate = (consentStatus: {
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}) => {
  gtmPush({
    event: "consent_update",
    consent_analytics: consentStatus.analytics,
    consent_marketing: consentStatus.marketing,
    consent_functional: consentStatus.functional,
  });
};

// Enable/disable GTM based on consent
export const updateGTMConsent = (consentStatus: {
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}) => {
  // Google Consent Mode v2
  gtmPush({
    event: "consent",
    consent: {
      ad_storage: consentStatus.marketing ? "granted" : "denied",
      analytics_storage: consentStatus.analytics ? "granted" : "denied",
      functionality_storage: consentStatus.functional ? "granted" : "denied",
      personalization_storage: consentStatus.marketing ? "granted" : "denied",
      security_storage: "granted",
    },
  });
};

// Initialize consent mode (call before GTM loads)
export const initializeConsentMode = () => {
  gtmPush({
    event: "consent",
    consent: {
      ad_storage: "denied",
      analytics_storage: "denied",
      functionality_storage: "denied",
      personalization_storage: "denied",
      security_storage: "granted",
    },
  });
};

// Debug utilities
export const debugGTM = {
  // Check if GTM is loaded
  isLoaded: () => {
    return !!window.google_tag_manager;
  },

  // Get current dataLayer contents
  getDataLayer: () => {
    return window.dataLayer || [];
  },

  // Test GTM with a debug event
  testEvent: () => {
    gtmPush({
      event: "debug_test",
      debug_timestamp: new Date().toISOString(),
      debug_url: window.location.href,
    });
    console.log("Debug event sent to GTM");
  },

  // Check consent status
  getConsentStatus: () => {
    const dataLayer = window.dataLayer || [];
    const consentEvents = dataLayer.filter(
      (item: Record<string, unknown>) => item.event === "consent"
    );
    return consentEvents[consentEvents.length - 1] || null;
  },

  // Monitor GTM loading
  monitorLoading: (timeout = 5000) => {
    const startTime = Date.now();
    const checkInterval = setInterval(() => {
      if (debugGTM.isLoaded()) {
        clearInterval(checkInterval);
        console.log(`GTM loaded successfully in ${Date.now() - startTime}ms`);
      } else if (Date.now() - startTime > timeout) {
        clearInterval(checkInterval);
        console.warn(`GTM failed to load within ${timeout}ms`);
      }
    }, 100);
  },
};

// Error handling wrapper for GTM functions
export const safeGTMPush = (data: Record<string, unknown>) => {
  try {
    gtmPush(data);
  } catch (error) {
    console.error("Error pushing to GTM dataLayer:", error);
  }
};
