import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Button } from "./ui";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleNavigation = (item: { href: string; isRoute: boolean }) => {
    if (item.isRoute) {
      navigate(item.href);
    } else if (item.href.startsWith("#")) {
      if (location.pathname !== "/") {
        navigate("/", { state: { scrollTo: item.href } });
      } else {
        const element = document.querySelector(item.href);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }
    }
    setIsMenuOpen(false);
  };

  return (
    <header
      className={`sticky top-0 z-50 backdrop-blur-md transition-all duration-300
      ${isScrolled ? "bg-theme-alt-bg/75 shadow-lg" : "bg-theme-alt-bg/30"}
    `}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20 w-full">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <Link
              to="/"
              onClick={(e) => {
                if (location.pathname === "/") {
                  e.preventDefault();

                  window.scrollTo({ top: 0, behavior: "smooth" });
                } else {
                  // navigate to / and scroll to #hero
                  e.preventDefault();
                  navigate("/");
                }
              }}
            >
              <img
                src="https://www.luminvibe.co.uk/images/agritram/logo.png"
                alt="Agritram Logo"
                className="h-10 w-auto"
              />
            </Link>
          </div>

          {/* CTA Buttons - Right aligned */}
          <div className="hidden lg:flex items-center space-x-4">
            <Button
              variant="ghost"
              size="md"
              onClick={() =>
                handleNavigation({ href: "/about", isRoute: true })
              }
              className="whitespace-nowrap"
            >
              About Team
            </Button>
            <Button
              variant="ghost"
              size="md"
              onClick={() =>
                handleNavigation({ href: "#early-access", isRoute: false })
              }
              className="whitespace-nowrap"
            >
              Get Early Access
            </Button>
            <Button
              variant="secondary"
              size="md"
              onClick={() =>
                handleNavigation({ href: "#partner", isRoute: false })
              }
              className="whitespace-nowrap"
            >
              Partner With Us
            </Button>
          </div>

          {/* Mobile/Tablet Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-2 rounded-md text-theme-primary-text hover:text-theme-link hover:bg-theme-main-bg transition-colors duration-200"
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-20 left-0 right-0 bg-theme-alt-bg/95 backdrop-blur-md shadow-lg border-t z-40">
            <div className="px-4 py-6 space-y-3">
              <Button
                variant="ghost"
                size="md"
                fullWidth
                onClick={() =>
                  handleNavigation({ href: "/about", isRoute: true })
                }
                className="justify-start text-left"
              >
                About Team
              </Button>
              <Button
                variant="ghost"
                size="md"
                fullWidth
                onClick={() =>
                  handleNavigation({ href: "#early-access", isRoute: false })
                }
                className="justify-start text-left"
              >
                Get Early Access
              </Button>
              <Button
                variant="primary"
                size="md"
                fullWidth
                onClick={() =>
                  handleNavigation({ href: "#partner", isRoute: false })
                }
                className="mt-4"
              >
                Partner With Us
              </Button>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
