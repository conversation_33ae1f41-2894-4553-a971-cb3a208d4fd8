import React, { useState, useEffect } from "react";
import {
  ChevronLeft,
  ChevronRight,
  Download,
  TrendingUp,
  Users,
  DollarSign,
  Target,
  Lightbulb,
  Shield,
  BarChart3,
  ArrowLeft,
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import PartnershipModal from "../components/PartnershipModal";
import { Button } from "../components/ui";
import Footer from "../components/Footer";

const PitchDeck: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const location = useLocation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPartnerType, setSelectedPartnerType] = useState("");

  useEffect(() => {
    if (location.state?.selectedPartnerType) {
      setSelectedPartnerType(location.state.selectedPartnerType);
    }
  }, [location.state?.selectedPartnerType]);

  const slides = [
    {
      id: 1,
      title: "The Problem",
      subtitle: "Critical Issues in Agricultural Finance",
      content: {
        type: "problem",
        data: {
          problems: [
            {
              icon: Users,
              title: "475 Million Farms Lack Collateral",
              description:
                "Global demand for agriculture credit is soaring as 475 million farms lack collateral to access loans.",
              impact: "Credit access gap",
            },
            {
              icon: BarChart3,
              title: "7–10 Days Settlement",
              description:
                "Average settlement times for cross-border payment settlements in the Agriculture trade.",
              impact: "Slow payments",
            },
            {
              icon: DollarSign,
              title: "$15–$45 SWIFT Fees",
              description:
                "Average per transaction fees charged by Banks & financial intermediaries for cross-border payments.",
              impact: "High transaction costs",
            },
          ],
        },
      },
      icon: Target,
      color: "red",
    },
    {
      id: 2,
      title: "Market Opportunity",
      subtitle: "Unlocking Trillions in Agricultural Liquidity",
      content: {
        type: "market",
        data: {
          description:
            "The intersection of Real world Asset tokenisation of Agricultural commodities & the proof-of-unlock mechanism is a once-in-a-generation business opportunity to unlock trillions in locked liquidity for farmers, traders, merchants, banks and manufacturers globally.",
          growth: "4.2% annual growth rate (projected to 2029)",
          segments: [
            {
              name: "Global Agriculture Trade Market",
              size: "$2.2T",
              growth: "4.2%",
            },
            {
              name: "Target Audience Demand",
              size: "$450B",
              growth: "",
            },
          ],
        },
      },
      icon: TrendingUp,
      color: "blue",
    },
    {
      id: 3,
      title: "Solution",
      subtitle: "",
      content: {
        type: "solution",
        data: {
          description:
            "A decentralised payments marketplace that pays you back interest when you pay for the agricultural commodities you order from the platform, settling your transaction in seconds, charging lower fees than the bank and allowing farmers to access loans without repayment periods.",
          solutions: [
            {
              icon: DollarSign,
              title: "Stablecoin Payments & Instant Settlement",
              description:
                "A stablecoin payments system enabling lower transaction fees & near-instant settlement times.",
              benefit:
                "Lower costs and instant cross-border transactions for all participants.",
            },
            {
              icon: Shield,
              title: "Real-world Asset Tokenization",
              description:
                "Real-world asset tokenization of agricultural commodities.",
              benefit:
                "Unlocks liquidity and enables transparent, secure trading of agricultural assets.",
            },
            {
              icon: Users,
              title: "Decentralised Lending & Borrowing",
              description:
                "A decentralised lending & borrowing protocol for all system participants.",
              benefit:
                "Enables farmers to access loans instantly, without traditional repayment periods or collateral.",
            },
          ],
        },
      },
      icon: Lightbulb,
      color: "emerald",
    },
    {
      id: 4,
      title: "Revenue Model",
      subtitle: "",
      content: {
        type: "business",
        data: {
          description:
            "We operate on a hybrid platform monetisation model, with charges starting at 2% per transaction.",
          streams: [
            {
              name: "Tokenisation, Detokenisation & Transaction fees",
              percentage: "2%+",
              description:
                "Fees charged for tokenising, detokenising, and processing transactions on the platform.",
            },
            {
              name: "Interest from Investment & holding of Reserves",
              percentage: "",
              description:
                "Earnings from investing and holding platform reserves.",
            },
            {
              name: "Partnerships with Agriculture traders & e-commerce platforms",
              percentage: "",
              description:
                "Revenue from collaborations with agriculture traders and agri e-commerce platforms.",
            },
          ],
          projectedRevenue: "", // You can fill this if you want to show a projection
        },
      },
      icon: DollarSign,
      color: "amber",
    },
    {
      id: 5,
      title: "Traction",
      subtitle: "",
      content: {
        type: "traction",
        data: {
          description:
            "The Agritram platform has secured a Letter of Intent (LOI) from one of Africa's largest Exporters partnering up with 300 cooperatives and 600,000 Farmers with an annual capacity of 800,000 metric tonnes and 54 commodities ready to be tokenised in the medium to long term establishing direct trade relationships between manufacturers and farmers.",
          metrics: [
            {
              label: "Tonnes of cocoa ready to be tokenised in Year 1",
              value: "4,200",
              growth: "",
            },
            {
              label: "Projected Revenue for Year 1",
              value: "£2,000,000",
              growth: "",
            },
            {
              label: "Farmers to be onboarded on the Platform in Year 1",
              value: "10,000",
              growth: "",
            },
          ],
          milestones: [],
        },
      },
      icon: BarChart3,
      color: "green",
    },
    {
      id: 6,
      title: "Total Addressable Market (TAM)",
      subtitle: "",
      content: {
        type: "tam",
        data: {
          geoTAM: [
            { region: "Ivory Coast", value: "$84.37 Billion", type: "Supply" },
            { region: "Ghana", value: "$39.319 Billion", type: "Supply" },
            { region: "Nigeria", value: "$8.13 Billion", type: "Supply" },
            { region: "Cameroon", value: "$7.81 Billion", type: "Supply" },
            {
              region: "United Kingdom",
              value: "$21.75 Billion",
              type: "Buyer",
            },
            { region: "Netherlands", value: "$131.2 Billion", type: "Buyer" },
            { region: "Belgium", value: "$67.15 Billion", type: "Buyer" },
            { region: "Germany", value: "$31.48 Billion", type: "Buyer" },
            { region: "Spain", value: "$22.02 Billion", type: "Buyer" },
            { region: "France", value: "$84.02 Billion", type: "Buyer" },
            { region: "Italy", value: "$22.07 Billion", type: "Buyer" },
          ],
          geoTAMTotal: "$519.31 Billion",
          userTAM: [
            { user: "Farmers", value: "$84.37 Billion", type: "Supply" },
            {
              user: "Traders/Exporters",
              value: "$8.13 Billion",
              type: "Supply",
            },
            { user: "Manufacturers", value: "$21.75 Billion", type: "Buyer" },
          ],
          userTAMTotal: "$114.25 Billion",
        },
      },
      icon: BarChart3,
      color: "blue",
    },
    {
      id: 7,
      title: "Thank You",
      subtitle: "Questions & Discussion",
      content: {
        type: "contact",
        data: {
          message:
            "Ready to transform agricultural finance with LuminVibe's AgriTram platform?",
          contacts: [
            { label: "Support", value: "<EMAIL>" },
            {
              label: "Direct Contact",
              value: "<EMAIL>",
            },
            { label: "LuminVibe Company", value: "www.luminvibe.co.uk" },
            { label: "Agritram", value: "www.agritram.com" },
            {
              label: "LuminVibe LinkedIn",
              value: "www.linkedin.com/company/luminvibe-technologies-limited/",
            },
            {
              label: "Agritram LinkedIn",
              value: "linkedin.com/company/luminvibe",
            },
          ],
          nextSteps: [
            "Schedule a platform demonstration",
            "Explore investment opportunities",
            "Discuss strategic partnerships",
            "Review tokenization roadmap",
            "Connect with our technical team",
          ],
        },
      },
      icon: Users,
      color: "emerald",
    },
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const currentSlideData = slides[currentSlide];

  const renderSlideContent = () => {
    const { content } = currentSlideData;

    switch (content.type) {
      case "problem":
        return (
          <div className="space-y-8">
            {content.data.problems?.map((problem, index) => {
              const IconComponent = problem.icon;
              return (
                <div
                  key={index}
                  className="bg-red-50 border-l-4 border-red-500 p-6 rounded-r-lg"
                >
                  <div className="flex items-start space-x-4">
                    <div className="bg-red-100 p-3 rounded-lg">
                      <IconComponent className="h-6 w-6 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-semibold text-gray-900 mb-2">
                        {problem.title}
                      </h4>
                      <p className="text-gray-700 mb-3">
                        {problem.description}
                      </p>
                      <div className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium inline-block">
                        Impact: {problem.impact}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        );

      case "solution":
        return (
          <div className="space-y-8">
            {content.data.solutions?.map((solution, index) => {
              const IconComponent = solution.icon;
              return (
                <div
                  key={index}
                  className="bg-emerald-50 border-l-4 border-emerald-500 p-6 rounded-r-lg"
                >
                  <div className="flex items-start space-x-4">
                    <div className="bg-emerald-100 p-3 rounded-lg">
                      <IconComponent className="h-6 w-6 text-emerald-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-semibold text-gray-900 mb-2">
                        {solution.title}
                      </h4>
                      <p className="text-gray-700 mb-3">
                        {solution.description}
                      </p>
                      <div className="bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-medium inline-block">
                        Benefit: {solution.benefit}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        );

      case "market":
        return (
          <div>
            {content.data.description && (
              <div className="mb-8 text-lg text-gray-700 text-center">
                {content.data.description}
              </div>
            )}
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="text-center bg-blue-50 p-6 rounded-lg">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  $2.2 Trillion
                </div>
                <div className="text-gray-600">
                  Global agriculture trade market (2029 projection)
                </div>
              </div>
              <div className="text-center bg-blue-50 p-6 rounded-lg">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  $450 Billion
                </div>
                <div className="text-gray-600">
                  Target audience demand for credit & payment solutions
                </div>
              </div>
            </div>
            <div className="bg-blue-100 p-6 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-800">
                Market Growth: {content.data.growth}
              </div>
            </div>
          </div>
        );

      case "business":
        return (
          <div className="space-y-8">
            <div className="space-y-6">
              {content.data.streams?.map((stream, index) => (
                <div key={index} className="bg-amber-50 p-6 rounded-lg">
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="text-xl font-semibold text-gray-900">
                      {stream.name}
                    </h4>
                    <div className="text-2xl font-bold text-amber-600">
                      {stream.percentage}
                    </div>
                  </div>
                  <p className="text-gray-700 mb-3">{stream.description}</p>
                </div>
              ))}
            </div>
          </div>
        );

      case "traction":
        return (
          <div className="space-y-8">
            <div className="grid md:grid-cols-2 gap-6">
              {content.data.metrics?.map((metric, index) => (
                <div
                  key={index}
                  className="bg-green-50 p-6 rounded-lg text-center"
                >
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {metric.value}
                  </div>
                  <div className="font-semibold text-gray-900 mb-1">
                    {metric.label}
                  </div>
                  <div className="text-green-700 text-sm">{metric.growth}</div>
                </div>
              ))}
            </div>
          </div>
        );
      case "contact":
        return (
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <p className="text-xl text-gray-600">{content.data.message}</p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {content.data.contacts?.map((contact, index) => {
                let href = "";

                if (
                  contact.label.includes("Support") ||
                  contact.label.includes("Contact")
                ) {
                  href = `mailto:${contact.value}`;
                } else if (
                  contact.label.includes("Agritram") ||
                  contact.label.includes("Company")
                ) {
                  href = contact.value.startsWith("http")
                    ? contact.value
                    : `https://${contact.value}`;
                } else if (contact.label === "LuminVibe LinkedIn") {
                  href = contact.value.startsWith("http")
                    ? contact.value
                    : `https://${contact.value}`;
                }

                return (
                  <div
                    key={index}
                    className="group bg-white border-2 border-emerald-100 hover:border-emerald-300 p-6 rounded-xl transition-all duration-300 hover:shadow-lg"
                  >
                    <div className="flex items-center mb-3">
                      <div className="font-semibold text-gray-900 text-lg">
                        {contact.label}
                      </div>
                    </div>
                    {href ? (
                      <a
                        href={href}
                        className="inline-flex items-center text-theme-link hover:text-theme-link-hover font-medium transition-all duration-200 decoration-2 underline-offset-4"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <span className="break-all">
                          {contact.label === "LuminVibe LinkedIn" ||
                          contact.label === "Agritram LinkedIn"
                            ? "linkedin.com"
                            : contact.value}
                        </span>
                        <svg
                          className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                          />
                        </svg>
                      </a>
                    ) : (
                      <div className="text-emerald-600 font-medium">
                        {contact.value}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 p-8 rounded-2xl border-2 border-emerald-200">
              <div className="text-center mb-6">
                <h4 className="text-2xl font-bold text-gray-900 mb-2">
                  Next Steps
                </h4>
                <p className="text-gray-600">Let's move forward together</p>
              </div>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {content.data.nextSteps?.map((step, index) => (
                  <div
                    key={index}
                    className="bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 border border-emerald-100"
                  >
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-8 h-8 bg-emerald-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-3">
                        {index + 1}
                      </div>
                      <span className="text-gray-700 font-medium leading-relaxed">
                        {step}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case "tam":
        return (
          <div className="space-y-8">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Geographical TAM Table */}
              <div>
                <div className="font-bold text-lg mb-2">
                  Geographical TAM (Phase 1 - Cocoa)
                </div>
                <table className="w-full bg-white rounded-lg overflow-hidden shadow-sm text-sm">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-4 py-2 text-left">Region</th>
                      <th className="px-4 py-2 text-right">TAM</th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Supply Side */}
                    <tr className="bg-gray-50">
                      <td className="px-4 py-2 font-semibold" colSpan={2}>
                        Supply Side TAM
                      </td>
                    </tr>
                    {content.data.geoTAM
                      ?.filter((r) => r.type === "Supply")
                      .map((row) => (
                        <tr key={row.region}>
                          <td className="px-4 py-2">{row.region}</td>
                          <td className="px-4 py-2 text-right">{row.value}</td>
                        </tr>
                      ))}
                    {/* Buyer Side */}
                    <tr className="bg-gray-50">
                      <td className="px-4 py-2 font-semibold" colSpan={2}>
                        Buyer Side TAM
                      </td>
                    </tr>
                    {content.data.geoTAM
                      ?.filter((r) => r.type === "Buyer")
                      .map((row) => (
                        <tr key={row.region}>
                          <td className="px-4 py-2">{row.region}</td>
                          <td className="px-4 py-2 text-right">{row.value}</td>
                        </tr>
                      ))}
                    {/* Total */}
                    <tr className="bg-gray-200 font-bold">
                      <td className="px-4 py-2">Total TAM</td>
                      <td className="px-4 py-2 text-right">
                        {content.data.geoTAMTotal}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              {/* User Based Classification TAM Table */}
              <div>
                <div className="font-bold text-lg mb-2">
                  User Based Classification of TAM (Phase 1 - Cocoa)
                </div>
                <table className="w-full bg-white rounded-lg overflow-hidden shadow-sm text-sm">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-4 py-2 text-left">User</th>
                      <th className="px-4 py-2 text-right">TAM</th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Supply Side */}
                    <tr className="bg-gray-50">
                      <td className="px-4 py-2 font-semibold" colSpan={2}>
                        Supply Side TAM (Per 1 tonne of Cocoa)
                      </td>
                    </tr>
                    {content.data.userTAM
                      ?.filter((r) => r.type === "Supply")
                      .map((row) => (
                        <tr key={row.user}>
                          <td className="px-4 py-2">{row.user}</td>
                          <td className="px-4 py-2 text-right">{row.value}</td>
                        </tr>
                      ))}
                    {/* Buyer Side */}
                    <tr className="bg-gray-50">
                      <td className="px-4 py-2 font-semibold" colSpan={2}>
                        Buyer Side TAM (Per 1 tonne of Cocoa)
                      </td>
                    </tr>
                    {content.data.userTAM
                      ?.filter((r) => r.type === "Buyer")
                      .map((row) => (
                        <tr key={row.user}>
                          <td className="px-4 py-2">{row.user}</td>
                          <td className="px-4 py-2 text-right">{row.value}</td>
                        </tr>
                      ))}
                    {/* Total */}
                    <tr className="bg-gray-200 font-bold">
                      <td className="px-4 py-2">Total TAM</td>
                      <td className="px-4 py-2 text-right">
                        {content.data.userTAMTotal}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Content not available</div>;
    }
  };

  const getColorClasses = (color: string) => {
    const colorMap = {
      emerald: "from-emerald-500 to-emerald-600",
      blue: "from-blue-500 to-blue-600",
      amber: "from-amber-500 to-amber-600",
      purple: "from-purple-500 to-purple-600",
      green: "from-green-500 to-green-600",
      red: "from-red-500 to-red-600",
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.emerald;
  };

  return (
    <div className="min-h-screen pt-16" style={{ backgroundColor: "#fff5ea" }}>
      {/* Header */}
      <header className="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link
              to="/"
              className="inline-flex items-center text-sm transition-colors duration-200 hover:text-opacity-70"
              style={{ color: "#2d4d31" }}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Link>
            <div className="flex items-center">
              <Link
                to="/"
                className="inline-flex items-center text-sm transition-colors duration-200 hover:text-opacity-70"
                style={{ color: "#2d4d31" }}
              >
                <img
                  src="https://www.luminvibe.co.uk/images/agritram/logo.png"
                  alt="Agritram Logo"
                  className="h-8"
                />
              </Link>
            </div>
          </div>
        </div>
      </header>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
            Investor Pitch Deck
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto px-2">
            Comprehensive overview of Agritram's vision, market opportunity, and
            growth strategy
          </p>
        </div>
        {/* Download Options */}
        <div className="text-center mb-8 sm:mb-12">
          <div className="p-6 sm:p-8 max-w-2xl mx-auto">
            <p className="text-sm sm:text-base font-bold text-gray-900 mb-4 sm:mb-6 px-2">
              Access our comprehensive Pitch Deck and White Paper to learn more,
              and connect with us to explore partnership opportunities.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4 sm:px-0">
              <Button
                variant="primary"
                size="lg"
                icon={Download}
                onClick={() => setIsModalOpen(true)}
                className="w-full sm:w-auto"
              >
                Download PDFs & Partner With Us
              </Button>
            </div>
          </div>
        </div>

        {/* Pitch Deck Container */}
        <div className="bg-white rounded-xl sm:rounded-2xl shadow-xl overflow-hidden">
          {/* Slide Header */}
          <div
            className={`bg-gradient-to-r ${getColorClasses(
              currentSlideData.color
            )} text-white p-4 sm:p-6`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 sm:space-x-4 min-w-0">
                <div className="bg-white/20 p-1.5 sm:p-2 rounded-lg flex-shrink-0">
                  <currentSlideData.icon className="h-4 w-4 sm:h-6 sm:w-6" />
                </div>
                <div className="min-w-0">
                  <h3 className="text-lg sm:text-xl lg:text-2xl font-bold truncate">
                    {currentSlideData.title}
                  </h3>
                  {currentSlideData.subtitle && (
                    <p className="text-white/90 text-sm sm:text-base truncate">
                      {currentSlideData.subtitle}
                    </p>
                  )}
                </div>
              </div>
              <div className="text-white/80 text-xs sm:text-sm flex-shrink-0">
                {currentSlide + 1}/{slides.length}
              </div>
            </div>
          </div>

          {/* Slide Content */}
          <div className="p-4 sm:p-6 lg:p-8 min-h-[300px] sm:min-h-[400px]">
            {renderSlideContent()}
          </div>

          {/* Navigation */}
          <div className="bg-gray-50 p-4 sm:p-6 flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={prevSlide}
              disabled={currentSlide === 0}
              icon={ChevronLeft}
              className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
            >
              <span className="hidden sm:inline">Previous</span>
              <span className="sm:hidden">Prev</span>
            </Button>

            {/* Slide Indicators */}
            <div className="flex space-x-1 sm:space-x-2">
              {slides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-colors duration-200 ${
                    index === currentSlide
                      ? "bg-emerald-600"
                      : "bg-gray-300 hover:bg-gray-400"
                  }`}
                />
              ))}
            </div>

            <Button
              variant="primary"
              size="sm"
              onClick={nextSlide}
              disabled={currentSlide === slides.length - 1}
              icon={ChevronRight}
              iconPosition="right"
              className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
            >
              <span className="hidden sm:inline">Next</span>
              <span className="sm:hidden">Next</span>
            </Button>
          </div>
        </div>
      </div>
      <PartnershipModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        initialPartnerType={selectedPartnerType}
      />
      <Footer />
    </div>
  );
};

export default PitchDeck;
