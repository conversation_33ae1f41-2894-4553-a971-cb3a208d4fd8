import React, { useState } from "react";
import { FileText, Download, Eye } from "lucide-react";
import { DocumentPreviewModal } from "./DocumentPreviewModal";
import { Button } from "./ui";
import { DownloadFormModal } from "./DownloadFormModal";

const WhitePaper: React.FC = () => {
  const [isDocumentPreviewOpen, setIsDocumentPreviewOpen] = useState(false);
  const [isDownloadFormOpen, setIsDownloadFormOpen] = useState(false);

  return (
    <>
      <section id="whitepaper" className="py-12 sm:py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12">
            <div className="text-center mb-8 sm:mb-12">
              <div
                className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 rounded-full mb-4 sm:mb-6"
                style={{ backgroundColor: "#2d4d31" }}
              >
                <FileText className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
              </div>

              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-black mb-4 sm:mb-6 px-2">
                Download Our{" "}
                <span style={{ color: "#2d4d31" }}>White Paper</span>
              </h2>

              <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto mb-6 sm:mb-8 px-2">
                Discover how blockchain technology and AI are revolutionizing
                agricultural supply chains and financial services.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4 sm:px-0">
              <Button
                variant="primary"
                size="lg"
                icon={Download}
                onClick={() => setIsDownloadFormOpen(true)}
                className="w-full sm:w-auto"
              >
                Get Free Access
              </Button>
              <Button
                variant="outline"
                size="lg"
                icon={Eye}
                onClick={() => setIsDocumentPreviewOpen(true)}
                className="w-full sm:w-auto"
              >
                Preview Online
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        isOpen={isDocumentPreviewOpen}
        onClose={() => setIsDocumentPreviewOpen(false)}
        documentUrl={
          "https://www.luminvibe.co.uk/images/agritram/whitepaper.pdf"
        }
        onDownloadFormOpen={() => setIsDownloadFormOpen(true)}
      />
      {/* Document Preview Modal */}
      <DownloadFormModal
        isOpen={isDownloadFormOpen}
        onClose={() => setIsDownloadFormOpen(false)}
        // onSubmit={handleDownload}
      />
    </>
  );
};

export default WhitePaper;
