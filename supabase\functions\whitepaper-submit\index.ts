import { createClient } from 'npm:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

interface WhitepaperRequest {
  fullName: string;
  email: string;
  company: string;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method Not Allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }

  try {
    // Parse request body
    const { fullName, email, company }: WhitepaperRequest = await req.json();

    // Validate required fields
    if (!fullName || !email || !company) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields: fullName, email, company' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return new Response(
        JSON.stringify({ error: 'Invalid email format' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    const resendApiKey = Deno.env.get('VITE_RESEND_API_KEY');

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Initialize Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Check if lead already exists
    const { data: existingLead, error: selectError } = await supabase
      .from('whitepaper_leads')
      .select('*')
      .eq('email', email)
      .maybeSingle();

    if (selectError) {
      console.error('Supabase select error:', selectError);
      throw new Error('Database query failed');
    }

    let leadId: string;
    let isNewLead = false;

    if (existingLead) {
      // Update existing lead: increment visits_count
      const { data: updatedLead, error: updateError } = await supabase
        .from('whitepaper_leads')
        .update({ 
          visits_count: existingLead.visits_count + 1,
          full_name: fullName, // Update name in case it changed
          company: company // Update company in case it changed
        })
        .eq('id', existingLead.id)
        .select()
        .single();

      if (updateError) {
        console.error('Supabase update error:', updateError);
        throw new Error('Failed to update lead data');
      }
      leadId = updatedLead.id;
    } else {
      // Insert new lead
      const { data: newLead, error: insertError } = await supabase
        .from('whitepaper_leads')
        .insert({ 
          full_name: fullName, 
          email, 
          company,
          visits_count: 1
        })
        .select()
        .single();

      if (insertError) {
        console.error('Supabase insert error:', insertError);
        throw new Error('Failed to insert new lead data');
      }
      leadId = newLead.id;
      isNewLead = true;
    }

    // Send automated email using Netlify function if Resend API key is available
    let emailSent = false;
    if (resendApiKey) {
      try {
        // Call the Netlify function for sending emails
        const emailResponse = await fetch('/.netlify/functions/sendEmail', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: email,
            name: fullName,
            company: company,
            type: 'download',
            isNewLead: isNewLead
          }),
        });

        if (emailResponse.ok) {
          emailSent = true;
          console.log('Email sent successfully to:', email);
        } else {
          const errorData = await emailResponse.text();
          console.error('Email sending error:', errorData);
        }
      } catch (emailError) {
        console.error('Email sending failed:', emailError);
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        leadId, 
        message: emailSent 
          ? 'Lead submitted and download email sent successfully' 
          : 'Lead submitted successfully',
        emailSent,
        isNewLead
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Edge Function error:', error);
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});