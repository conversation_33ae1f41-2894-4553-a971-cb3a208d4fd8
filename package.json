{"name": "agritram", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "js-cookie": "^3.0.5", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-cookie-consent": "^9.0.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "resend": "^4.6.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/postcss": "^4.1.11", "@types/js-cookie": "^3.0.6", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}