import React, { useEffect } from "react";
import Header from "../components/Header";
import Footer from "../components/Footer";
import AdvisoryBoard from "../components/about/AdvisoryBoard";
import OurTeam from "../components/about/OurTeam";
import { useLocation } from "react-router-dom";

const AboutUs: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    if (location.state && location.state.scrollTo) {
      const anchor = location.state.scrollTo;
      const element = document.querySelector(anchor);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
      // Remove scrollTo from history state so it doesn't scroll again on back/forward
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  return (
    <div className="min-h-screen bg-theme-alt-bg">
      <Header />
      <OurTeam />
      <AdvisoryBoard />
      <Footer />
    </div>
  );
};

export default AboutUs;
