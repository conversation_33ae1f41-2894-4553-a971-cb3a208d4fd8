import { createClient } from 'npm:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

interface PreviewRequest {
  email: string;
  type: 'preview' | 'download';
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method Not Allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }

  try {
    // Parse request body
    const { email, type }: PreviewRequest = await req.json();

    // Validate required fields
    if (!email || !type) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields: email, type' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return new Response(
        JSON.stringify({ error: 'Invalid email format' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    const resendApiKey = Deno.env.get('VITE_RESEND_API_KEY');

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Initialize Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Check if lead exists in database
    const { data: existingLead, error: selectError } = await supabase
      .from('whitepaper_leads')
      .select('*')
      .eq('email', email)
      .maybeSingle();

    if (selectError) {
      console.error('Supabase select error:', selectError);
      return new Response(
        JSON.stringify({ error: 'Database query failed' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    if (!existingLead) {
      return new Response(
        JSON.stringify({ 
          error: 'Access denied. Please submit your information first to access the preview.' 
        }),
        { 
          status: 403, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Generate a secure preview URL with token
    const token = btoa(`${email}:${Date.now()}:${type}`);
    
    // Use the actual PDF file from the public directory
    const previewUrl = `/whitepaper.pdf#toolbar=0&navpanes=0&scrollbar=0`;

    // Update the lead's visits count if this is a preview request
    if (type === 'preview') {
      await supabase
        .from('whitepaper_leads')
        .update({ 
          visits_count: existingLead.visits_count + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingLead.id);
    }

    // Send preview email if Resend API key is available
    let emailSent = false;
    if (resendApiKey && type === 'preview') {
      try {
        // Call the Netlify function for sending preview emails
        const emailResponse = await fetch('/.netlify/functions/sendEmail', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: email,
            name: existingLead.full_name,
            company: existingLead.company,
            type: 'preview'
          }),
        });

        if (emailResponse.ok) {
          emailSent = true;
          console.log('Preview email sent successfully to:', email);
        } else {
          const errorData = await emailResponse.text();
          console.error('Preview email sending error:', errorData);
        }
      } catch (emailError) {
        console.error('Preview email sending failed:', emailError);
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        previewUrl,
        message: 'Preview URL generated successfully',
        emailSent
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Edge Function error:', error);
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});