/*
# Add visits_count to pitch_deck_requests table

1. Schema Changes
   - Add `visits_count` column to `pitch_deck_requests` table
   - Set default value to 1 for new records
   - Update existing records to have visits_count = 1

2. Purpose
   - Track how many times a user has requested pitch deck access
   - Similar functionality to whitepaper_leads table
   - Helps with analytics and partnership engagement tracking
*/

-- Add visits_count column to pitch_deck_requests table
ALTER TABLE pitch_deck_requests 
ADD COLUMN IF NOT EXISTS visits_count integer DEFAULT 1;

-- Update existing records to have visits_count = 1
UPDATE pitch_deck_requests 
SET visits_count = 1 
WHERE visits_count IS NULL;