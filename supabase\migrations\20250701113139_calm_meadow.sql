/*
  # Create early_access_leads table

  1. New Tables
    - `early_access_leads`
      - `id` (uuid, primary key)
      - `full_name` (text, required)
      - `email` (text, unique, required)
      - `role` (text, required)
      - `company` (text, required)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `early_access_leads` table
    - Add policies for anonymous inserts/updates and service role access
    - Add trigger for automatic updated_at updates
*/

CREATE TABLE IF NOT EXISTS early_access_leads (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  full_name text NOT NULL,
  email text UNIQUE NOT NULL,
  role text NOT NULL,
  company text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE early_access_leads ENABLE ROW LEVEL SECURITY;

-- Create policies for access control
CREATE POLICY "Allow anonymous inserts for early access leads"
  ON early_access_leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

CREATE POLICY "Allow anonymous updates for early access leads"
  ON early_access_leads
  FOR UPDATE
  TO anon
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Allow anonymous selects for early access leads"
  ON early_access_leads
  FOR SELECT
  TO anon
  USING (true);

CREATE POLICY "Service role can manage all early access leads"
  ON early_access_leads
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can manage early access leads"
  ON early_access_leads
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Add index for faster email lookups
CREATE INDEX IF NOT EXISTS idx_early_access_leads_email ON early_access_leads (email);

-- Create trigger to call the function on update (reusing existing function)
CREATE TRIGGER update_early_access_leads_updated_at
  BEFORE UPDATE ON early_access_leads
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();