import React, { useEffect } from "react";
import About from "../components/About";
import ComingSoon from "../components/ComingSoon";
import Header from "../components/Header";
import Hero from "../components/Hero";
import Features from "../components/Features";
import Partner from "../components/Partner";
import Footer from "../components/Footer";
import Contact from "../components/Contact";
import EarlyAccess from "../components/EarlyAccess";
import VideoSection from "../components/VideoSection";
import WhitePaper from "../components/WhitePaper";
import { useLocation } from "react-router-dom";

const LandingPage: React.FC = () => {
  const location = useLocation();
  useEffect(() => {
    if (location.state && location.state.scrollTo) {
      const anchor = location.state.scrollTo;
      const element = document.querySelector(anchor);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
      // Remove scrollTo from history state so it doesn't scroll again on back/forward
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  return (
    <div className="min-h-screen">
      <Header />
      <Hero />
      <ComingSoon />
      <About />
      <VideoSection />
      <Features />
      <Partner />
      <WhitePaper />
      <EarlyAccess />
      <Contact />
      <Footer />
    </div>
  );
};

export default LandingPage;
