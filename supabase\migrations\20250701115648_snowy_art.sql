/*
  # Revert contact messages to create new records

  1. Schema Changes
    - Change `subject` back to single text field (not array)
    - Keep visits_count for tracking individual message interactions
    
  2. Behavior Changes
    - Each contact form submission creates a new record
    - No more array handling for subjects
    - Each message is treated as a separate inquiry
*/

-- Change subject back to text (not array)
ALTER TABLE contact_messages 
ADD COLUMN IF NOT EXISTS subject_text text;

-- For existing records, take the first element from the array if it exists
UPDATE contact_messages 
SET subject_text = COALESCE(subject[1], 'General Inquiry')
WHERE subject_text IS NULL;

-- Drop the array column and rename the text column
ALTER TABLE contact_messages DROP COLUMN IF EXISTS subject;
ALTER TABLE contact_messages RENAME COLUMN subject_text TO subject;

-- Make subject NOT NULL with a default
ALTER TABLE contact_messages 
ALTER COLUMN subject SET NOT NULL,
ALTER COLUMN subject SET DEFAULT 'General Inquiry';