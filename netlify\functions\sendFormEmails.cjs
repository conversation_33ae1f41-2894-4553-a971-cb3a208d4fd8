// Netlify Function for sending automated emails using Resend
// Handles all four form types: Contact, Early Access, Whitepaper, Partnership

const { Resend } = require("resend");
const createEmailHeader = (headerText) => `
  <div style="background: linear-gradient(135deg, #FFF5EA 0%, #F0F9FF 50%, #FFFFFF 100%); padding: 48px 24px; text-align: center; border-bottom: 4px solid #55CC00; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);">
    <div style="max-width: 600px; margin: 0 auto;">
      <h1 style="color: #1F2937; font-size: 32px; font-weight: 700; margin: 0; font-family: 'Inter', Arial, sans-serif; line-height: 1.2; letter-spacing: -0.02em;">
        ${headerText}
      </h1>
    </div>
  </div>
`;

const createEmailFooter = () => `
  <div style="background: linear-gradient(135deg, #F9FAFB 0%, #FFF5EA 100%); padding: 48px 24px 32px; border-top: 2px solid #E5E7EB;">
    <div style="max-width: 600px; margin: 0 auto;">
      <!-- Company Info -->
      <div style="text-align: center; margin-bottom: 32px;">
        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 24px; flex-wrap: wrap; gap: 16px;">
          <img src="https://www.luminvibe.co.uk/images/favicon.ico" alt="LuminVibe" style="height: 32px; width: 32px;" />
          <div style="width: 2px; height: 32px; background: linear-gradient(180deg, #55CC00, #10B981); border-radius: 1px;"></div>
          <div style="text-align: center;">
            <p style="margin: 0; font-size: 12px; color: #6B7280; font-weight: 500; font-family: 'Inter', Arial, sans-serif;">A LuminVibe Project</p>
            <img src="https://www.luminvibe.co.uk/images/agritram/logo.png" alt="Agritram" style="height: 28px; margin-top: 4px;" />
          </div>
        </div>
        <p style="color: #4B5563; font-size: 15px; line-height: 1.6; margin: 0; font-family: 'Inter', Arial, sans-serif; max-width: 400px; margin: 0 auto;">
          Revolutionizing agricultural finance through blockchain transparency.<br>
          <span style="color: #6B7280;">Connecting farmers, traders, and manufacturers in a trusted ecosystem.</span>
        </p>
      </div>

      <!-- Contact Info -->
      <div style="text-align: center; margin-bottom: 32px;">
        <div style="background-color: #FFFFFF; border-radius: 8px; padding: 16px; display: inline-block; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
          <div style="margin-bottom: 8px;">
            <span style="color: #1F2937; font-size: 14px; font-weight: 500; font-family: 'Inter', Arial, sans-serif;"><EMAIL></span>
          </div>
          <div>
            <span style="color: #6B7280; font-size: 14px; font-family: 'Inter', Arial, sans-serif;">London, United Kingdom</span>
          </div>
        </div>
      </div>

      <!-- Copyright -->
      <div style="text-align: center; border-top: 1px solid #E5E7EB; padding-top: 24px;">
        <p style="color: #6B7280; font-size: 13px; margin: 0; font-family: 'Inter', Arial, sans-serif;">
          Copyright © ${new Date().getFullYear()} LuminVibe & Agritram - All rights reserved
        </p>
      </div>
    </div>
  </div>
`;

// 1. Get in Touch Form Email Template
const createContactEmailTemplate = (name, email, subject, message) => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Thanks for reaching out to Agritram!</title>
</head>
<body style="margin: 0; padding: 0; font-family: 'Inter', Arial, sans-serif; background-color: #f9fafb;">
  ${createEmailHeader("Thanks for reaching out to Agritram!")}

  <div style="background-color: #ffffff; padding: 48px 24px;">
    <div style="max-width: 600px; margin: 0 auto;">

      <p style="color: #1F2937; font-size: 18px; line-height: 1.5; margin-bottom: 32px; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">
        Hi ${name},
      </p>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 32px; font-family: 'Inter', Arial, sans-serif;">
        Thank you for contacting Agritram — where agriculture meets the future of finance. We're <span style="color: #55CC00; font-weight: 600;">thrilled</span> to hear from you!
      </p>

      <div style="background: linear-gradient(135deg, #FFF5EA 0%, #F0F9FF 100%); border-left: 4px solid #55CC00; padding: 24px; margin: 32px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);">
        <h3 style="color: #1F2937; font-size: 18px; font-weight: 600; margin: 0 0 16px 0; font-family: 'Inter', Arial, sans-serif;">
          Your Message Details:
        </h3>
        <div style="background-color: #FFFFFF; border-radius: 6px; padding: 16px; margin-bottom: 16px;">
          <p style="color: #374151; font-size: 14px; margin: 8px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong style="color: #1F2937;">Subject:</strong> ${subject}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 8px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong style="color: #1F2937;">Email:</strong> ${email}
          </p>
        </div>
        <div>
          <p style="color: #1F2937; font-size: 14px; margin: 0 0 8px 0; font-family: 'Inter', Arial, sans-serif; font-weight: 600;">
            Message:
          </p>
          <div style="color: #374151; font-size: 14px; line-height: 1.6; padding: 16px; background-color: #FFFFFF; border-radius: 6px; border: 1px solid #E5E7EB; font-family: 'Inter', Arial, sans-serif;">
            ${message}
          </div>
        </div>
      </div>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 32px; font-family: 'Inter', Arial, sans-serif;">
        Our team is reviewing your message and will get back to you within the next <strong style="color: #1F2937;">24–48 hours</strong>.
      </p>

      <div style="background: linear-gradient(135deg, #ECFDF5 0%, #F0FDF4 100%); border: 2px solid #10B981; padding: 24px; margin: 32px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(16, 185, 129, 0.1);">
        <h3 style="color: #1F2937; font-size: 18px; font-weight: 600; margin: 0 0 16px 0; font-family: 'Inter', Arial, sans-serif;">
          In the meantime, feel free to:
        </h3>
        <ul style="color: #374151; font-size: 15px; line-height: 1.6; margin: 0; padding-left: 20px; font-family: 'Inter', Arial, sans-serif; list-style-type: disc;">
          <li style="margin-bottom: 12px;">Watch our 2-minute <a href="https://agritram.luminvibe.co.uk#video" style="color: #10B981; text-decoration: underline; text-decoration-color: #10B981; text-underline-offset: 2px; font-weight: 600; transition: color 0.2s ease;">Intro Video</a></li>
          <li style="margin-bottom: 0;">Or reach out directly at <a href="mailto:<EMAIL>" style="color: #10B981; text-decoration: underline; text-decoration-color: #10B981; text-underline-offset: 2px; font-weight: 600; transition: color 0.2s ease;"><EMAIL></a></li>
        </ul>
      </div>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 32px; font-family: 'Inter', Arial, sans-serif; text-align: center;">
        We appreciate your interest in <strong style="color: #55CC00;">transforming agri-supply chains</strong> with us.
      </p>

      <div style="text-align: center; margin: 40px 0;">
        <table style="margin: 0 auto; border-spacing: 0;">
          <tr>
            <td style="padding: 0 8px 16px 0;">
              <a href="https://www.luminvibe.co.uk/images/agritram/Agritram-Whitepaper.pdf" style="display: inline-block; background: linear-gradient(135deg, #55CC00, #10B981); color: #000000; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; font-family: 'Inter', Arial, sans-serif; box-shadow: 0 4px 6px rgba(85, 204, 0, 0.2); transition: all 0.2s ease;">
                Download White Paper
              </a>
            </td>
          </tr>
          <tr>
            <td style="padding: 0;">
              <a href="https://agritram.com" style="display: inline-block; background-color: #1F2937; color: #ffffff; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; font-family: 'Inter', Arial, sans-serif; box-shadow: 0 4px 6px rgba(31, 41, 55, 0.2); transition: all 0.2s ease;">
                Explore Platform
              </a>
            </td>
          </tr>
        </table>
      </div>

      <div style="text-align: center; margin-top: 48px; padding-top: 24px; border-top: 1px solid #E5E7EB;">
        <p style="color: #6B7280; font-size: 15px; line-height: 1.6; margin: 0; font-family: 'Inter', Arial, sans-serif;">
          Warm regards,<br>
          <strong style="color: #1F2937; font-weight: 600;">The Agritram Team</strong><br>
          <span style="color: #9CA3AF;">LuminVibe & Agritram</span>
        </p>
      </div>
    </div>
  </div>

  ${createEmailFooter()}
</body>
</html>
`;

// 2. Early Access Benefits Email Template
const createEarlyAccessEmailTemplate = (name, email, role, company) => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to Agritram Early Access</title>
</head>
<body style="margin: 0; padding: 0; font-family: 'Inter', Arial, sans-serif; background-color: #f9fafb;">
  ${createEmailHeader("Welcome to Agritram Early Access")}

  <div style="background-color: #ffffff; padding: 48px 24px;">
    <div style="max-width: 600px; margin: 0 auto;">

      <p style="color: #1F2937; font-size: 20px; line-height: 1.5; margin-bottom: 32px; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">
        Hi <strong style="color: #55CC00; font-weight: 700;">${name}</strong>,
      </p>

      <p style="color: #374151; font-size: 17px; line-height: 1.6; margin-bottom: 32px; font-family: 'Inter', Arial, sans-serif;">
        We're <strong style="color: #55CC00; font-weight: 600;">excited</strong> to officially welcome you to Agritram's Early Access Program!
      </p>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 40px; font-family: 'Inter', Arial, sans-serif;">
        As an early user from <strong style="color: #1F2937; font-weight: 600;">${company}</strong>, you're now part of an exclusive community that's revolutionizing agricultural finance through blockchain transparency.
      </p>

      <!-- Hero Benefits Section -->
      <div style="background: linear-gradient(135deg, #ECFDF5 0%, #F0FDF4 100%); border: 2px solid #10B981; padding: 32px; margin: 40px 0; border-radius: 12px; text-align: center; box-shadow: 0 4px 6px rgba(16, 185, 129, 0.1);">
        <h2 style="color: #1F2937; font-size: 24px; font-weight: 700; margin: 0 0 24px 0; font-family: 'Inter', Arial, sans-serif; letter-spacing: -0.01em;">
          Your Exclusive Early Access Benefits
        </h2>
        <div style="text-align: left; max-width: 520px; margin: 0 auto;">
          <ul style="color: #374151; font-size: 16px; line-height: 1.6; margin: 0; padding-left: 0; font-family: 'Inter', Arial, sans-serif; list-style: none;">
            <li style="margin-bottom: 20px; padding: 16px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px; border-left: 4px solid #10B981;">
              <strong style="color: #1F2937; font-size: 17px; font-weight: 600;">Priority Onboarding</strong><br>
              <span style="color: #4B5563; font-size: 15px; line-height: 1.5; margin-top: 4px; display: block;">Be among the first to list, trade, or buy. Get dedicated support and early operational setup.</span>
            </li>
            <li style="margin-bottom: 20px; padding: 16px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px; border-left: 4px solid #10B981;">
              <strong style="color: #1F2937; font-size: 17px; font-weight: 600;">Early Advantage in a Growing Marketplace</strong><br>
              <span style="color: #4B5563; font-size: 15px; line-height: 1.5; margin-top: 4px; display: block;">Secure strategic positioning in a verified trade network before the full-scale rollout begins.</span>
            </li>
            <li style="margin-bottom: 20px; padding: 16px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px; border-left: 4px solid #10B981;">
              <strong style="color: #1F2937; font-size: 17px; font-weight: 600;">Direct Access to Verified Farmer & Co-op Networks</strong><br>
              <span style="color: #4B5563; font-size: 15px; line-height: 1.5; margin-top: 4px; display: block;">Connect early with 600,000+ producers and 300+ cooperatives</span>
            </li>
            <li style="margin-bottom: 0; padding: 16px; background-color: rgba(255, 255, 255, 0.7); border-radius: 8px; border-left: 4px solid #10B981;">
              <strong style="color: #1F2937; font-size: 17px; font-weight: 600;">Early Visibility in a Verified Supply Chain</strong><br>
              <span style="color: #4B5563; font-size: 15px; line-height: 1.5; margin-top: 4px; display: block;">Position your brand or operation at the front of a fully traceable, tokenised trade network.</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Call to Action Section -->
      <div style="background: linear-gradient(135deg, #FFF5EA 0%, #F0F9FF 100%); border-left: 4px solid #55CC00; padding: 32px; margin: 40px 0; border-radius: 12px; box-shadow: 0 2px 4px rgba(85, 204, 0, 0.1);">
        <h3 style="color: #1F2937; font-size: 22px; font-weight: 700; margin: 0 0 16px 0; font-family: 'Inter', Arial, sans-serif; text-align: center; letter-spacing: -0.01em;">
          Your Journey to Transforming Agricultural Finance Begins Now
        </h3>
        <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 24px; font-family: 'Inter', Arial, sans-serif; text-align: center;">
          Ready to dive deeper? Here's what you can do next:
        </p>

        <div style="text-align: center; margin: 32px 0;">
          <a href="https://www.luminvibe.co.uk/images/agritram/Agritram-Whitepaper.pdf" style="display: inline-block; background: linear-gradient(135deg, #1F2937, #374151); color: #ffffff; padding: 16px 40px; text-decoration: none; border-radius: 10px; font-weight: 700; font-size: 17px; font-family: 'Inter', Arial, sans-serif; box-shadow: 0 6px 12px rgba(31, 41, 55, 0.3); transition: all 0.2s ease; letter-spacing: 0.01em;">
            Download White Paper Now
          </a>
        </div>
      </div>

      <!-- Registration Details -->
      <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; padding: 20px; margin: 25px 0; border-radius: 8px;">
        <h3 style="color: #000000; font-size: 16px; font-weight: 600; margin: 0 0 15px 0; font-family: 'Inter', Arial, sans-serif;">
          Your Registration Details:
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Name:</strong> ${name}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Role:</strong> ${role}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Email:</strong> ${email}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Company:</strong> ${company}
          </p>
        </div>
      </div>

      <!-- Support Section -->
      <div style="background-color: #fef3c7; border: 1px solid #f59e0b; padding: 20px; margin: 25px 0; border-radius: 8px; text-align: center;">
        <h3 style="color: #000000; font-size: 18px; font-weight: 600; margin: 0 0 10px 0; font-family: 'Inter', Arial, sans-serif;">
          Questions? We're Here for You!
        </h3>
        <p style="color: #374151; font-size: 14px; line-height: 1.6; margin-bottom: 15px; font-family: 'Inter', Arial, sans-serif;">
          Our team is ready to help you make the most of your early access experience.
        </p>
        <a href="mailto:<EMAIL>" style="color: #f59e0b; text-decoration: none; font-weight: 600; font-size: 16px; font-family: 'Inter', Arial, sans-serif;">
          <EMAIL>
        </a>
      </div>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin: 30px 0 25px 0; font-family: 'Inter', Arial, sans-serif;">
        Thanks for being part of this revolution. Together, we're building the future of agricultural finance!
      </p>

      <p style="color: #6b7280; font-size: 14px; line-height: 1.6; margin-top: 30px; font-family: 'Inter', Arial, sans-serif;">
        Warm regards,<br>
        <strong style="color: #000000;">The Agritram Team</strong><br>
        LuminVibe & Agritram
      </p>
    </div>
  </div>

  ${createEmailFooter()}
</body>
</html>
`;

// 3. Download Whitepaper Email Template
const createWhitepaperEmailTemplate = (name, email, company) => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Your Agritram White Paper is Ready – Dive In!</title>
</head>
<body style="margin: 0; padding: 0; font-family: 'Inter', Arial, sans-serif; background-color: #f9fafb;">
  ${createEmailHeader("Your Agritram White Paper is Ready – Dive In!")}

  <div style="background-color: #ffffff; padding: 48px 24px;">
    <div style="max-width: 600px; margin: 0 auto;">

      <p style="color: #1F2937; font-size: 18px; line-height: 1.5; margin-bottom: 32px; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">
        Hi <strong style="color: #55CC00; font-weight: 700;">${name}</strong>,
      </p>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 32px; font-family: 'Inter', Arial, sans-serif;">
        Thank you for your interest in Agritram — where we're <strong style="color: #55CC00; font-weight: 600;">reimagining agriculture</strong> with blockchain and financial technology.
      </p>

      <!-- Vision Statement -->
      <div style="background: linear-gradient(135deg, #ECFDF5 0%, #F0FDF4 100%); border: 2px solid #10B981; padding: 32px; margin: 40px 0; border-radius: 12px; text-align: center; box-shadow: 0 4px 6px rgba(16, 185, 129, 0.1);">
        <p style="color: #1F2937; font-size: 19px; font-weight: 600; line-height: 1.5; margin: 0; font-family: 'Inter', Arial, sans-serif; letter-spacing: -0.01em;">
          Discover how Agritram is transforming the agri-supply chain through <strong style="color: #10B981;">blockchain transparency</strong>, <strong style="color: #10B981;">fintech innovation</strong>, and <strong style="color: #10B981;">sustainable trade finance</strong>.
        </p>
      </div>

      <div style="text-align: center; background: linear-gradient(135deg, #FFF5EA 0%, #F0F9FF 100%); padding: 24px; border-radius: 8px; margin: 32px 0;">
        <p style="color: #374151; font-size: 16px; line-height: 1.6; margin: 0; font-family: 'Inter', Arial, sans-serif;">
          Your exclusive whitepaper is now ready for download and has been <strong style="color: #55CC00;">saved to your device</strong>.
        </p>
      </div>

      <!-- What's Inside Section -->
      <div style="background: linear-gradient(135deg, #FFF5EA 0%, #F0F9FF 100%); border-left: 4px solid #55CC00; padding: 32px; margin: 32px 0; border-radius: 12px; box-shadow: 0 2px 4px rgba(85, 204, 0, 0.1);">
        <h3 style="color: #1F2937; font-size: 22px; font-weight: 700; margin: 0 0 24px 0; font-family: 'Inter', Arial, sans-serif; text-align: center; letter-spacing: -0.01em;">
          Inside, you'll discover:
        </h3>
        <ul style="color: #374151; font-size: 16px; line-height: 1.6; margin: 0; padding-left: 0; font-family: 'Inter', Arial, sans-serif; list-style: none;">
          <li style="margin-bottom: 16px; padding: 16px; background-color: rgba(255, 255, 255, 0.8); border-radius: 8px; border-left: 4px solid #10B981;">
            <strong style="color: #1F2937; font-weight: 600;">Blockchain-powered traceability</strong> from farm to factory
          </li>
          <li style="margin-bottom: 16px; padding: 16px; background-color: rgba(255, 255, 255, 0.8); border-radius: 8px; border-left: 4px solid #10B981;">
            <strong style="color: #1F2937; font-weight: 600;">Stablecoin finance</strong> for smallholder farmers
          </li>
          <li style="margin-bottom: 0; padding: 16px; background-color: rgba(255, 255, 255, 0.8); border-radius: 8px; border-left: 4px solid #10B981;">
            <strong style="color: #1F2937; font-weight: 600;">ESG and compliance</strong> built into the supply chain
          </li>
        </ul>
      </div>

      <!-- Next Steps Section -->
      <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; padding: 25px; margin: 25px 0; border-radius: 8px;">
        <h3 style="color: #000000; font-size: 20px; font-weight: 600; margin: 0 0 15px 0; font-family: 'Inter', Arial, sans-serif;">
          Ready to explore further?
        </h3>
        <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 20px; font-family: 'Inter', Arial, sans-serif;">
          We're excited to have you on this journey. If you'd like to explore <strong>partnership</strong> or <strong>investment opportunities</strong>, here's how to get started:
        </p>

        <div style="text-align: center; margin: 25px 0;">
          <a href="https://agritram.com#partner" style="display: inline-block; background-color: #000000; color: #ffffff; padding: 15px 35px; text-decoration: none; border-radius: 8px; font-weight: 700; font-size: 16px; margin: 0 10px 15px 0; font-family: 'Inter', Arial, sans-serif; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            Join Early Partners
          </a>
        </div>
      </div>

      <!-- Download Details -->
      <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; padding: 20px; margin: 25px 0; border-radius: 8px;">
        <h3 style="color: #000000; font-size: 16px; font-weight: 600; margin: 0 0 15px 0; font-family: 'Inter', Arial, sans-serif;">
          Your Download Details:
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Name:</strong> ${name}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Company:</strong> ${company}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Email:</strong> ${email}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Download Date:</strong> ${new Date().toLocaleDateString()}
          </p>
        </div>
      </div>

      <!-- Support Section -->
      <div style="background-color: #fef3c7; border: 1px solid #f59e0b; padding: 20px; margin: 25px 0; border-radius: 8px; text-align: center;">
        <h3 style="color: #000000; font-size: 18px; font-weight: 600; margin: 0 0 10px 0; font-family: 'Inter', Arial, sans-serif;">
          Have questions? Contact us anytime!
        </h3>
        <p style="color: #374151; font-size: 14px; line-height: 1.6; margin-bottom: 15px; font-family: 'Inter', Arial, sans-serif;">
          Our team is ready to discuss how Agritram can transform your agricultural operations.
        </p>
        <a href="mailto:<EMAIL>" style="color: #f59e0b; text-decoration: none; font-weight: 600; font-size: 16px; font-family: 'Inter', Arial, sans-serif;">
          <EMAIL>
        </a>
      </div>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin: 30px 0 25px 0; font-family: 'Inter', Arial, sans-serif;">
        Together, we're <strong style="color: #55CC00;">cultivating the future</strong>.
      </p>

      <p style="color: #6b7280; font-size: 14px; line-height: 1.6; margin-top: 30px; font-family: 'Inter', Arial, sans-serif;">
        Warm regards,<br>
        <strong style="color: #000000;">The Agritram Team</strong><br>
        LuminVibe & Agritram
      </p>
    </div>
  </div>

  ${createEmailFooter()}
</body>
</html>
`;

// 4. Partnership Email Template
const createPartnershipEmailTemplate = (
  contactName,
  email,
  companyName,
  phone,
  partnerType
) => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Thank You for Your Partnership Interest – Let's Build the Future Together</title>
</head>
<body style="margin: 0; padding: 0; font-family: 'Inter', Arial, sans-serif; background-color: #f9fafb;">
  ${createEmailHeader("Thank You for Your Partnership Interest – Let's Build the Future Together")}

  <div style="background-color: #ffffff; padding: 40px 20px;">
    <div style="max-width: 600px; margin: 0 auto;">

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 25px; font-family: 'Inter', Arial, sans-serif;">
        Hi <strong style="color: #000000;">${contactName}</strong>,
      </p>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 25px; font-family: 'Inter', Arial, sans-serif;">
        Thank you for expressing interest in partnering with Agritram.
      </p>

      <!-- Vision Statement -->
      <div style="background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%); border: 2px solid #10b981; padding: 25px; margin: 30px 0; border-radius: 12px; text-align: center;">
        <p style="color: #000000; font-size: 18px; font-weight: 600; line-height: 1.5; margin: 0; font-family: 'Inter', Arial, sans-serif;">
          We're on a mission to connect farmers, traders, manufacturers, and financial institutions through a <strong>transparent, blockchain-powered supply chain</strong>.
        </p>
      </div>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 25px; font-family: 'Inter', Arial, sans-serif;">
        We believe strong partnerships are key to transforming agricultural finance. As a potential partner, you can look forward to:
      </p>

      <!-- Partnership Benefits Section -->
      <div style="background: linear-gradient(135deg, #FFF5EA 0%, #F0F9FF 100%); border-left: 4px solid #55CC00; padding: 32px; margin: 32px 0; border-radius: 12px; box-shadow: 0 2px 4px rgba(85, 204, 0, 0.1);">
        <ul style="color: #374151; font-size: 16px; line-height: 1.6; margin: 0; padding-left: 0; font-family: 'Inter', Arial, sans-serif; list-style: none;">
          <li style="margin-bottom: 16px; padding: 16px; background-color: rgba(255, 255, 255, 0.8); border-radius: 8px; border-left: 4px solid #10B981;">
            <strong style="color: #1F2937; font-weight: 600;">Early access to our blockchain-powered agri-finance platform</strong>
          </li>
          <li style="margin-bottom: 16px; padding: 16px; background-color: rgba(255, 255, 255, 0.8); border-radius: 8px; border-left: 4px solid #10B981;">
            <strong style="color: #1F2937; font-weight: 600;">Co-development opportunities</strong>
          </li>
          <li style="margin-bottom: 0; padding: 16px; background-color: rgba(255, 255, 255, 0.8); border-radius: 8px; border-left: 4px solid #10B981;">
            <strong style="color: #1F2937; font-weight: 600;">ESG-aligned value creation and data-driven transparency</strong>
          </li>
        </ul>
      </div>

      <!-- Next Steps Section -->
      <div style="background-color: #ecfdf5; border: 1px solid #10b981; padding: 25px; margin: 25px 0; border-radius: 8px;">
        <h3 style="color: #000000; font-size: 20px; font-weight: 600; margin: 0 0 15px 0; font-family: 'Inter', Arial, sans-serif;">
          What Happens Next:
        </h3>
        <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 15px; font-family: 'Inter', Arial, sans-serif;">
          Our partnerships team will be in touch within <strong>1–2 business days</strong> to explore synergies and schedule a call.
        </p>
        <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 0; font-family: 'Inter', Arial, sans-serif;">
          If you'd like to fast-track the conversation, feel free to reply directly or email <a href="mailto:<EMAIL>" style="color: #55CC00; text-decoration: none; font-weight: 600;"><EMAIL></a>.
        </p>
      </div>

      <!-- Partnership Details -->
      <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; padding: 20px; margin: 25px 0; border-radius: 8px;">
        <h3 style="color: #000000; font-size: 16px; font-weight: 600; margin: 0 0 15px 0; font-family: 'Inter', Arial, sans-serif;">
          Your Partnership Inquiry Details:
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Contact Name:</strong> ${contactName}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Company:</strong> ${companyName}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Email:</strong> ${email}
          </p>
          ${
            phone
              ? `<p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Phone:</strong> ${phone}
          </p>`
              : ""
          }
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Partnership Type:</strong> ${partnerType}
          </p>
          <p style="color: #374151; font-size: 14px; margin: 5px 0; font-family: 'Inter', Arial, sans-serif;">
            <strong>Inquiry Date:</strong> ${new Date().toLocaleDateString()}
          </p>
        </div>
      </div>

      <p style="color: #374151; font-size: 16px; line-height: 1.6; margin: 30px 0 25px 0; font-family: 'Inter', Arial, sans-serif;">
        We're excited about the possibilities of working together.
      </p>

      <p style="color: #6b7280; font-size: 14px; line-height: 1.6; margin-top: 30px; font-family: 'Inter', Arial, sans-serif;">
        Warm regards,<br>
        <strong style="color: #000000;">The Agritram Team</strong><br>
        LuminVibe & Agritram
      </p>
    </div>
  </div>

  ${createEmailFooter()}
</body>
</html>
`;

// Initialize Resend with API key from environment variables
const resend = new Resend(process.env.VITE_RESEND_API_KEY);

// Email configuration
// Use Resend sandbox for testing, replace with your verified domain for production
const FROM_EMAIL = "Agritram <<EMAIL>>";

exports.handler = async (event, context) => {
  // Handle CORS preflight requests
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
      },
      body: "",
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ error: "Method not allowed" }),
    };
  }

  // Check if Resend API key is configured
  if (!process.env.VITE_RESEND_API_KEY && !process.env.RESEND_API_KEY) {
    console.error(
      "VITE_RESEND_API_KEY or RESEND_API_KEY environment variable is not set"
    );
    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ error: "Email service not configured" }),
    };
  }

  try {
    const requestData = JSON.parse(event.body);
    const { formType, ...formData } = requestData;

    // Validate required fields based on form type
    let emailTemplate, subject, recipientEmail, recipientName;

    switch (formType) {
      case "contact":
        const { name, email, subject: messageSubject, message } = formData;
        if (!name || !email || !messageSubject) {
          throw new Error("Missing required fields for contact form");
        }
        emailTemplate = createContactEmailTemplate(
          name,
          email,
          messageSubject,
          message || "No additional message provided."
        );
        subject = "Thank You for Contacting Agritram - We'll Be in Touch Soon!";
        recipientEmail = email;
        recipientName = name;
        break;

      case "early-access":
        const { name: eaName, email: eaEmail, role, company } = formData;
        if (!eaName || !eaEmail || !role || !company) {
          throw new Error("Missing required fields for early access form");
        }
        emailTemplate = createEarlyAccessEmailTemplate(
          eaName,
          eaEmail,
          role,
          company
        );
        subject =
          "Welcome to Agritram Early Access - Exclusive Benefits Await!";
        recipientEmail = eaEmail;
        recipientName = eaName;
        break;

      case "whitepaper":
        const {
          fullName,
          email: wpEmail,
          company: wpCompany,
          isNewLead = true,
        } = formData;
        if (!fullName || !wpEmail || !wpCompany) {
          throw new Error("Missing required fields for whitepaper form");
        }
        emailTemplate = createWhitepaperEmailTemplate(
          fullName,
          wpEmail,
          wpCompany
        );
        subject = "Your Agritram White Paper is Ready – Dive In!";
        recipientEmail = wpEmail;
        recipientName = fullName;
        break;

      case "partnership":
        const {
          contactName,
          email: partnerEmail,
          companyName,
          phone,
          partnerType,
        } = formData;
        if (!contactName || !partnerEmail || !companyName || !partnerType) {
          throw new Error("Missing required fields for partnership form");
        }
        emailTemplate = createPartnershipEmailTemplate(
          contactName,
          partnerEmail,
          companyName,
          phone,
          partnerType
        );
        subject =
          "Thank You for Your Partnership Interest - Let's Collaborate!";
        recipientEmail = partnerEmail;
        recipientName = contactName;
        break;

      default:
        throw new Error("Invalid form type specified");
    }

    // Send email using Resend
    const emailResponse = await resend.emails.send({
      from: FROM_EMAIL,
      to: [recipientEmail],
      subject: subject,
      html: emailTemplate,
    });

    console.log("Email sent successfully:", {
      formType,
      recipientEmail,
      emailId: emailResponse.data?.id,
    });

    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        success: true,
        message: "Email sent successfully",
        emailId: emailResponse.data?.id,
      }),
    };
  } catch (error) {
    console.error("Email sending error:", error);

    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        success: false,
        error: error.message || "Failed to send email",
      }),
    };
  }
};
