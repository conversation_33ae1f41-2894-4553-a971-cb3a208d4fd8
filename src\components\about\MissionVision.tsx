import { Eye, Target } from "lucide-react";

const MissionVision = () => {
  return (
    <div className="my-12 sm:my-16 lg:my-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">
        <div className="bg-theme-alt-bg p-6 sm:p-8 rounded-xl sm:rounded-2xl border border-primary-500/10 shadow-soft hover:shadow-medium transition-all duration-300">
          <div className="flex items-center mb-4 sm:mb-6">
            <div className="bg-theme-button-bg p-2 sm:p-3 rounded-lg mr-3 sm:mr-4 flex-shrink-0">
              <Target className="h-5 w-5 sm:h-6 sm:w-6 text-theme-button-text" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-theme-primary-text">
              Our Mission
            </h3>
          </div>
          <p className="text-sm sm:text-base lg:text-lg text-theme-primary-text leading-relaxed">
            Agritram is committed to eliminating high transaction fees, slow
            settlement times, and restricted access to financial instruments in
            the agricultural sector. We achieve this by providing a secure,
            transparent financial technology framework that tokenizes real-world
            commodities, enables verified smart contract trading, and ensures
            instant, stablecoin-based settlements.
          </p>
        </div>

        <div className="bg-theme-alt-bg p-6 sm:p-8 rounded-xl sm:rounded-2xl border border-secondary-500/10 shadow-soft hover:shadow-medium transition-all duration-300">
          <div className="flex items-center mb-4 sm:mb-6">
            <div className="bg-theme-link p-2 sm:p-3 rounded-lg mr-3 sm:mr-4 flex-shrink-0">
              <Eye className="h-5 w-5 sm:h-6 sm:w-6 text-theme-button-text" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-theme-primary-text">
              Our Vision
            </h3>
          </div>
          <p className="text-sm sm:text-base lg:text-lg text-theme-primary-text leading-relaxed">
            To revolutionize global agricultural trade by establishing a
            trusted, transparent, and efficient blockchain-powered
            infrastructure that empowers producers and connects global markets,
            ultimately contributing to global food security through
            decentralized financial interventions.
          </p>
        </div>
      </div>
    </div>
  );
};

export default MissionVision;
