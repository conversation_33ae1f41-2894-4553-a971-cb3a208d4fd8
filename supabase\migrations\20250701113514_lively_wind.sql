/*
# Add visits_count to contact_messages table

1. Schema Changes
   - Add `visits_count` column to `contact_messages` table
   - Set default value to 1 for new records
   - Update existing records to have visits_count = 1

2. Purpose
   - Track how many times a user has submitted contact messages
   - Similar functionality to whitepaper_leads table
   - Helps with analytics and customer support engagement tracking
*/

-- Add visits_count column to contact_messages table
ALTER TABLE contact_messages 
ADD COLUMN IF NOT EXISTS visits_count integer DEFAULT 1;

-- Update existing records to have visits_count = 1
UPDATE contact_messages 
SET visits_count = 1 
WHERE visits_count IS NULL;