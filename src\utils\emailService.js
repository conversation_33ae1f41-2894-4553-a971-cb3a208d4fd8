// Email Service Utility for sending automated emails
// Integrates with Netlify Functions and Resend

/**
 * Send automated email for contact form submissions
 * @param {Object} formData - Contact form data
 * @param {string} formData.name - Contact name
 * @param {string} formData.email - Contact email
 * @param {string} formData.subject - Message subject
 * @param {string} formData.message - Message content
 */
export const sendContactEmail = async (formData) => {
  try {
    const response = await fetch("/.netlify/functions/sendFormEmails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        formType: "contact",
        ...formData,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || "Failed to send contact email");
    }

    return result;
  } catch (error) {
    console.error("Contact email error:", error);
    throw error;
  }
};

/**
 * Send automated email for early access form submissions
 * @param {Object} formData - Early access form data
 * @param {string} formData.name - User name
 * @param {string} formData.email - User email
 * @param {string} formData.role - User role
 * @param {string} formData.company - Company name
 */
export const sendEarlyAccessEmail = async (formData) => {
  try {
    const response = await fetch("/.netlify/functions/sendFormEmails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        formType: "early-access",
        ...formData,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || "Failed to send early access email");
    }

    return result;
  } catch (error) {
    console.error("Early access email error:", error);
    throw error;
  }
};

/**
 * Send automated email for whitepaper download submissions
 * @param {Object} formData - Whitepaper form data
 * @param {string} formData.fullName - User full name
 * @param {string} formData.email - User email
 * @param {string} formData.company - Company name
 */
export const sendWhitepaperEmail = async (formData) => {
  try {
    const response = await fetch("/.netlify/functions/sendFormEmails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        formType: "whitepaper",
        ...formData,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || "Failed to send whitepaper email");
    }

    return result;
  } catch (error) {
    console.error("Whitepaper email error:", error);
    throw error;
  }
};

/**
 * Send automated email for partnership form submissions
 * @param {Object} formData - Partnership form data
 * @param {string} formData.contactName - Contact name
 * @param {string} formData.email - Contact email
 * @param {string} formData.companyName - Company name
 * @param {string} formData.phone - Phone number (optional)
 * @param {string} formData.partnerType - Type of partnership
 */
export const sendPartnershipEmail = async (formData) => {
  try {
    const response = await fetch("/.netlify/functions/sendFormEmails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        formType: "partnership",
        ...formData,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || "Failed to send partnership email");
    }

    return result;
  } catch (error) {
    console.error("Partnership email error:", error);
    throw error;
  }
};

/**
 * Generic email sender for any form type
 * @param {string} formType - Type of form (contact, early-access, whitepaper, partnership)
 * @param {Object} formData - Form data specific to the form type
 */
export const sendFormEmail = async (formType, formData) => {
  const emailSenders = {
    contact: sendContactEmail,
    "early-access": sendEarlyAccessEmail,
    whitepaper: sendWhitepaperEmail,
    partnership: sendPartnershipEmail,
  };

  const sender = emailSenders[formType];
  if (!sender) {
    throw new Error(`Unsupported form type: ${formType}`);
  }

  return await sender(formData);
};

// Email validation utility
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Form data validation utilities
export const validateContactForm = (formData) => {
  const { name, email, subject, message } = formData;
  const errors = [];

  if (!name?.trim()) errors.push("Name is required");
  if (!email?.trim()) errors.push("Email is required");
  else if (!validateEmail(email)) errors.push("Valid email is required");
  if (!subject?.trim()) errors.push("Subject is required");
  if (!message?.trim()) errors.push("Message is required");

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateEarlyAccessForm = (formData) => {
  const { name, email, role, company } = formData;
  const errors = [];

  if (!name?.trim()) errors.push("Name is required");
  if (!email?.trim()) errors.push("Email is required");
  else if (!validateEmail(email)) errors.push("Valid email is required");
  if (!role?.trim()) errors.push("Role is required");
  if (!company?.trim()) errors.push("Company is required");

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateWhitepaperForm = (formData) => {
  const { fullName, email, company } = formData;
  const errors = [];

  if (!fullName?.trim()) errors.push("Full name is required");
  if (!email?.trim()) errors.push("Email is required");
  else if (!validateEmail(email)) errors.push("Valid email is required");
  if (!company?.trim()) errors.push("Company is required");

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validatePartnershipForm = (formData) => {
  const { contactName, email, companyName, partnerType } = formData;
  const errors = [];

  if (!contactName?.trim()) errors.push("Contact name is required");
  if (!email?.trim()) errors.push("Email is required");
  else if (!validateEmail(email)) errors.push("Valid email is required");
  if (!companyName?.trim()) errors.push("Company name is required");
  if (!partnerType?.trim()) errors.push("Partner type is required");

  return {
    isValid: errors.length === 0,
    errors,
  };
};
