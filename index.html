<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>
      Agritram
    </title>
    <meta
      name="description"
      content="Revolutionizing agricultural finance through blockchain transparency. Faster payments, lower fees, and complete transparency for farmers, traders, and manufacturers."
    />
    <meta
      name="keywords"
      content="agricultural finance, blockchain agriculture, agritech, agricultural trade, smart contracts, agricultural payments, supply chain transparency, agricultural technology, fintech agriculture, sustainable agriculture"
    />
    <meta name="author" content="LuminVibe & Agritram" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.agritram.com/" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://agritram.com/" />
    <meta
      property="og:title"
      content="Agritram"
    />
    <meta
      property="og:description"
      content="Revolutionizing agricultural finance through blockchain transparency. Faster payments, lower fees, and complete transparency for farmers and traders."
    />
    <meta
      property="og:image"
      content="https://www.luminvibe.co.uk/images/agritram/og-image.png"
    />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta
      property="og:image:alt"
      content="Agritram - The Financial Engine for Agricultural Trade"
    />
    <meta property="og:site_name" content="Agritram" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="https://agritram.com/" />
    <meta
      name="twitter:title"
      content="Agritram"
    />
    <meta
      name="twitter:description"
      content="Revolutionizing agricultural finance through blockchain transparency. Faster payments, lower fees, and complete transparency for farmers, traders, and manufacturers."
    />
    <meta
      name="twitter:image"
      content="https://www.luminvibe.co.uk/images/agritram/og-image.png"
    />
    <meta
      name="twitter:image:alt"
      content="Agritram - The Financial Engine for Agricultural Trade"
    />
    <meta name="twitter:creator" content="@Agritram" />
    <meta name="twitter:site" content="@Agritram" />

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link
      rel="icon"
      type="image/png"
      sizes="192x192"
      href="/android-chrome-192x192.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="512x512"
      href="/android-chrome-512x512.png"
    />

    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#2D4D31" />
    <meta name="msapplication-TileColor" content="#2D4D31" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Agritram",
        "alternateName": "LuminVibe Agritram",
        "url": "https://agritram.com",
        "logo": "https://www.luminvibe.co.uk/images/agritram/logo.png",
        "description": "Revolutionizing agricultural finance through blockchain transparency. Faster payments, lower fees, and complete transparency for farmers and traders.",
        "foundingDate": "2023",
        "industry": "Agricultural Technology, Financial Technology, Blockchain",
        "parentOrganization": {
          "@type": "Organization",
          "name": "LuminVibe",
          "url": "https://www.luminvibe.co.uk"
        },
        "contactPoint": [
          {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "email": "<EMAIL>",
            "areaServed": ["GB", "EU"],
            "availableLanguage": "English"
          },
          {
            "@type": "ContactPoint",
            "contactType": "sales",
            "email": "<EMAIL>",
            "areaServed": ["GB", "EU"],
            "availableLanguage": "English",
            "description": "Sales Team Head for UK and Europe"
          }
        ],
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "London",
          "addressCountry": "GB"
        },
        "sameAs": [
          "https://www.linkedin.com/company/agritram/",
          "https://www.youtube.com/@Agritram"
        ],
        "offers": {
          "@type": "Service",
          "name": "Agricultural Trade Finance Platform",
          "description": "Blockchain-powered financial engine for agricultural trade with smart contracts, instant payments, and supply chain transparency"
        },
        "keywords": "agricultural finance, blockchain agriculture, agritech, agricultural trade, smart contracts, agricultural payments, supply chain transparency"
      }
    </script>

    <!-- Google Tag Manager - Conditional Loading -->
    <script>
      // Initialize dataLayer
      window.dataLayer = window.dataLayer || [];

      // Function to load GTM
      function loadGTM() {
        (function (w, d, s, l, i) {
          w[l] = w[l] || [];
          w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
          var f = d.getElementsByTagName(s)[0],
            j = d.createElement(s),
            dl = l != "dataLayer" ? "&l=" + l : "";
          j.async = true;
          j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
          f.parentNode.insertBefore(j, f);
        })(window, document, "script", "dataLayer", "GTM-58F4LHM6");
      }

      // Check for existing consent and load GTM if analytics consent is given
      function checkConsentAndLoadGTM() {
        try {
          const consent = document.cookie
            .split("; ")
            .find((row) => row.startsWith("agritram_cookie_consent="));

          if (consent) {
            const consentData = JSON.parse(
              decodeURIComponent(consent.split("=")[1])
            );
            if (consentData.analytics) {
              loadGTM();
            }
          }
        } catch (error) {
          console.log("No existing consent found");
        }
      }

      // Check consent on page load
      checkConsentAndLoadGTM();

      // Listen for consent updates
      window.addEventListener("cookieConsentUpdated", function (event) {
        if (event.detail.analytics && !window.gtag) {
          loadGTM();
        }
      });
    </script>
    <!-- End Google Tag Manager -->
  </head>
  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-58F4LHM6"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
