import React, { useState, useEffect, useRef, useCallback } from "react";
import { X, Maximize2 } from "lucide-react";

interface DocumentPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  documentUrl: string;
  onDownloadFormOpen: () => void;
}

export const DocumentPreviewModal: React.FC<DocumentPreviewModalProps> = ({
  isOpen,
  onClose,
  documentUrl,
  onDownloadFormOpen,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [error, setError] = useState<string>("");
  const modalRef = useRef<HTMLDivElement>(null);

  function download() {
    onClose();
    onDownloadFormOpen();
  }

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
      setIsFullscreen(false);
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  const exitFullscreen = useCallback(() => {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    }
    setIsFullscreen(false);
  }, []);

  const enterFullscreen = useCallback(() => {
    if (modalRef.current && modalRef.current.requestFullscreen) {
      modalRef.current.requestFullscreen();
      setIsFullscreen(true);
    }
  }, []);

  const handleClose = useCallback(() => {
    if (isFullscreen) {
      exitFullscreen();
    }
    onClose();
  }, [isFullscreen, exitFullscreen, onClose]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case "Escape":
          handleClose();
          break;
        case "f":
        case "F":
          if (isFullscreen) {
            exitFullscreen();
          } else {
            enterFullscreen();
          }
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, isFullscreen, handleClose, exitFullscreen, enterFullscreen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-75 backdrop-blur-sm"
        onClick={handleClose}
      />

      {/* Modal */}
      <div
        ref={modalRef}
        className={`relative bg-white shadow-2xl ${
          isFullscreen
            ? "w-full h-full rounded-none"
            : "w-full h-full sm:w-11/12 sm:h-5/6 max-w-6xl rounded-lg sm:rounded-xl"
        } flex flex-col`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 sm:p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg sm:rounded-t-xl">
          <div className="flex items-center space-x-2 sm:space-x-4 min-w-0">
            <h3 className="text-sm sm:text-lg font-semibold text-black truncate">
              Document Preview
            </h3>
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
            {/* Fullscreen - Hide on small screens */}
            <button
              onClick={isFullscreen ? exitFullscreen : enterFullscreen}
              className="hidden sm:block p-2 rounded-lg hover:bg-gray-200 transition-colors"
              title="Fullscreen (F)"
            >
              <Maximize2 className="h-4 w-4" />
            </button>

            <div className="hidden sm:block w-px h-6 bg-gray-300 mx-2" />

            {/* Close */}
            <button
              onClick={handleClose}
              className="p-1.5 sm:p-2 rounded-lg hover:bg-gray-200 transition-colors"
              title="Close (Esc)"
            >
              <X className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 relative overflow-hidden bg-gray-100">
          <div className="w-full h-full p-2 sm:p-4">
            <div className="h-full bg-white rounded-lg shadow-lg overflow-hidden">
              {error ? (
                <div className="flex items-center justify-center h-full flex-col p-4">
                  <div className="text-red-500 font-medium mb-3 sm:mb-4 text-sm sm:text-base text-center">
                    {error}
                  </div>
                  <button
                    onClick={() => window.open(documentUrl, "_blank")}
                    className="px-3 py-2 sm:px-4 sm:py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm sm:text-base"
                  >
                    Open in new tab
                  </button>
                </div>
              ) : (
                <iframe
                  src={documentUrl}
                  className="w-full h-full border-0"
                  title="PDF Viewer"
                  onError={() => setError("Failed to load PDF directly")}
                />
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-3 sm:p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg sm:rounded-b-xl">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <div className="text-xs sm:text-sm text-gray-600 text-center sm:text-left">
              <span className="hidden sm:inline">
                Press F for fullscreen •{" "}
              </span>
              Esc to close
            </div>
            <div className="text-xs sm:text-sm text-gray-500 text-center sm:text-right">
              To access the full version of this document, please{" "}
              <button
                type="button"
                onClick={download}
                className="text-theme-link hover:text-theme-link-hover font-medium underline"
              >
                click here
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
