import React, { useState } from "react";
import { Mail, CheckCircle, Users, Zap, Shield, Star } from "lucide-react";
import { supabase } from "../lib/supabase";
import { Button } from "./ui";
// @ts-expect-error: No type definitions for emailService.cjs
import { sendEarlyAccessEmail } from "../utils/emailService";

interface IFormData {
  name: string;
  email: string;
  role: string;
  company: string;
}

const EarlyAccess: React.FC = () => {
  const [formData, setFormData] = useState<IFormData>({
    name: "",
    email: "",
    role: "",
    company: "",
  });

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState<number>(60);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsSubmitting(true);
    setError(null);

    try {
      // Check if lead already exists
      const { data: existingLead, error: selectError } = await supabase
        .from("early_access_leads")
        .select("*")
        .eq("email", formData.email)
        .maybeSingle();

      if (selectError) {
        console.error("Supabase select error:", selectError);
        throw new Error("Database query failed");
      }

      if (existingLead) {
        // Update existing lead: increment visits_count
        // Add new role to array if not already present
        const currentRoles = existingLead.role || [];
        const updatedRoles = currentRoles.includes(formData.role)
          ? currentRoles
          : [...currentRoles, formData.role];

        const { error: updateError } = await supabase
          .from("early_access_leads")
          .update({
            visits_count: existingLead.visits_count + 1,
            full_name: formData.name, // Update name in case it changed
            role: updatedRoles, // Add new role to array
            company: formData.company, // Update company in case it changed
          })
          .eq("id", existingLead.id)
          .select()
          .single();

        if (updateError) {
          console.error("Supabase update error:", updateError);
          throw new Error("Failed to update lead data");
        }
      } else {
        // Insert new lead
        const { error: insertError } = await supabase
          .from("early_access_leads")
          .insert({
            full_name: formData.name,
            email: formData.email,
            role: [formData.role], // Store as array
            company: formData.company,
            visits_count: 1,
          })
          .select()
          .single();

        if (insertError) {
          console.error("Supabase insert error:", insertError);
          throw new Error("Failed to insert new lead data");
        }
      }
      // Send automated email
      try {
        await sendEarlyAccessEmail(formData);
        console.log("Thank you email sent successfully");
      } catch (emailError) {
        console.error("Failed to send thank you email:", emailError);
      }

      // Success - show success message
      setIsSubmitted(true);
      setCountdown(60);

      // Start countdown timer
      const countdownInterval = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(countdownInterval);
            setIsSubmitted(false);
            setFormData({
              name: "",
              email: "",
              role: "",
              company: "",
            });
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (err) {
      console.error("Early access submission error:", err);
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const earlyAccessBenefits = [
    {
      icon: Star,
      title: "Priority Onboarding",
      description:
        "Be among the first to list, trade, or buy. Get dedicated support and early operational setup.",
    },
    {
      icon: Users,
      title: "Early Advantage in a Growing Marketplace",
      description:
        "Secure strategic positioning in a verified trade network before the full-scale rollout begins.",
    },
    {
      icon: Zap,
      title: "Direct Access to Verified Farmer & Co-op Networks",
      description:
        "Connect early with 600,000+ producers and 300+ cooperatives",
    },
    {
      icon: Shield,
      title: "Early Visibility in a Verified Supply Chain",
      description:
        "Position your brand or operation at the front of a fully traceable, tokenised trade network.",
    },
  ];

  if (isSubmitted) {
    return (
      <section
        id="early-access"
        className="py-12 sm:py-16 lg:py-20 bg-emerald-50"
      >
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 lg:p-12 shadow-lg">
            <div className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4">
              This message will disappear in {countdown} seconds
            </div>
            <div className="bg-emerald-100 w-16 h-16 sm:w-20 sm:h-20 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
              <CheckCircle className="h-8 w-8 sm:h-10 sm:w-10 text-emerald-600" />
            </div>
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
              Welcome to Agritram Early Access!
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 px-2">
              Thank you for joining our exclusive early access program. We'll be
              in touch soon with updates and platform access details.
            </p>
            <div className="bg-emerald-50 rounded-lg p-4 sm:p-6">
              <p className="text-sm sm:text-base text-emerald-800 font-medium">
                Check your email for a confirmation and next steps. Our team
                will contact you within 48 hours.
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section
      id="early-access"
      className="py-12 sm:py-16 lg:py-20 bg-emerald-50"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 sm:gap-12 items-start">
          {/* Benefits */}
          <div className="space-y-6 sm:space-y-8 order-2 lg:order-1">
            <div>
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 text-center lg:text-left">
                Early Access Benefits
              </h3>
              <div className="space-y-4 sm:space-y-6">
                {earlyAccessBenefits.map((benefit, index) => {
                  const IconComponent = benefit.icon;
                  return (
                    <div
                      key={index}
                      className="flex items-start space-x-3 sm:space-x-4"
                    >
                      <div className="bg-emerald-100 p-2 sm:p-3 rounded-lg flex-shrink-0">
                        <IconComponent className="h-5 w-5 sm:h-6 sm:w-6 text-emerald-600" />
                      </div>
                      <div>
                        <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1 sm:mb-2">
                          {benefit.title}
                        </h4>
                        <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                          {benefit.description}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Sign-up Form */}
          <div className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 shadow-lg order-1 lg:order-2">
            <div className="flex items-center mb-4 sm:mb-6 justify-center lg:justify-start">
              <Mail className="h-5 w-5 sm:h-6 sm:w-6 text-emerald-600 mr-2 sm:mr-3" />
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900">
                Join Early Access
              </h3>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4">
                  <p className="text-red-700 text-xs sm:text-sm">{error}</p>
                </div>
              )}

              <div className="grid sm:grid-cols-2 gap-3 sm:gap-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 text-sm sm:text-base"
                    placeholder="Your full name"
                  />
                </div>
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 text-sm sm:text-base"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid sm:grid-cols-2 gap-3 sm:gap-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                    Role/Position *
                  </label>
                  <select
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 text-sm sm:text-base"
                  >
                    <option value="">Select your role</option>
                    <option value="farmer">Farmer</option>
                    <option value="trader">Trader</option>
                    <option value="manufacturer">Manufacturer</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                    Company/Organization *
                  </label>
                  <input
                    type="text"
                    required
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                    className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 text-sm sm:text-base"
                    placeholder="Your organization"
                  />
                </div>
              </div>

              {isSubmitting ? (
                <Button
                  variant="primary"
                  size="md"
                  isLoading
                  fullWidth
                  className="py-3 sm:py-4"
                >
                  Submitting...
                </Button>
              ) : (
                <Button
                  variant="primary"
                  size="md"
                  type="submit"
                  fullWidth
                  icon={Mail}
                  className="py-3 sm:py-4"
                >
                  Join Early Access Program
                </Button>
              )}

              <p className="text-xs sm:text-sm text-gray-500 text-center leading-relaxed px-2">
                By signing up, you agree to receive updates about Agritram. We
                respect your privacy and won't spam you.
              </p>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EarlyAccess;
