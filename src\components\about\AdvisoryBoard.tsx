import React from "react";
import "./OurTeam.css";

interface AdvisorMember {
  id: number;
  name: string;
  position: string;
  company: string;
  image: string;
  linkedin?: string;
}

const AdvisoryBoard: React.FC = () => {
  const advisors: AdvisorMember[] = [
    {
      id: 1,
      name: "<PERSON>",
      position: "Strategic Advisor, Cocoa Industry",
      company:
        "Managing Director of York Cocoa Works - “One of the most influential voices in the cocoa industry” - Cocoa Radar",
      image: "https://www.luminvibe.co.uk/images/agritram/sophie.png",
      linkedin: "https://www.linkedin.com/in/sophiejewett/",
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "Strategic Advisor, Partnerships & Marketing",
      company:
        "Ex-HP, Vodafone, and BT; sales leader recognized by The Sunday Times",
      image: "https://www.luminvibe.co.uk/images/agritram/keith.png",
      linkedin: "https://www.linkedin.com/in/keithrozelleb2bsales/",
    },
    {
      id: 3,
      name: "<PERSON><PERSON> <PERSON>",
      position: "Strategic Advisor, Cybersecurity & Privacy",
      company:
        "Cybersecurity expert at the University of York, part of the prestigious Russell Group.",
      image: "https://www.luminvibe.co.uk/images/agritram/vasileios.png",
      linkedin: "https://www.linkedin.com/in/vassilakisuk/",
    },
    {
      id: 4,
      name: "Ahmed Mujtaba",
      position: "Strategic Advisor, Supply Chain Management & Monitoring",
      company:
        "Ex- DHL, Asda, Sony. Supply chain management expert in agricultural commodities trade",
      image: "https://www.luminvibe.co.uk/images/agritram/ahmed.png",
      linkedin: "https://www.linkedin.com/in/ahmed-mujtaba1/",
    },
    {
      id: 5,
      name: "Dr. Victorien Edi",
      position: "Regional Agricultural Commodities Advisor, Africa",
      company:
        "President of SOCIMAA Cooperative and a key voice in African agricultural commodities trade.",
      image: "https://www.luminvibe.co.uk/images/agritram/ako.png",
      linkedin: "https://www.linkedin.com/in/socimaa-scoops-81166625b/",
    },
    {
      id: 6,
      name: "Dr. Sia Shahandashti",
      position: "Strategic Advisor, Blockchain Technology",
      company:
        "Blockchain and cryptography expert; Head of Cybersecurity at the University of York (Russell Group).",
      image: "https://www.luminvibe.co.uk/images/agritram/sia.png",
      linkedin: "https://www.linkedin.com/in/siamakfs/",
    },
    {
      id: 7,
      name: "Dr. Shane Hamilton",
      position: "Strategic Advisor, Agribusiness, Trade & Agriculture",
      company:
        "Agribusiness and trade expert at the University of York with deep insights into global agricultural systems.",
      image: "https://www.luminvibe.co.uk/images/agritram/shane.png",
      linkedin: "https://www.linkedin.com/in/shane-hamilton-2382b8125/",
    },
  ];

  return (
    <div
      id="advisory"
      className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-12 sm:mt-16 lg:mt-20 pt-12 sm:pt-16 lg:pt-20"
    >
      <div className="mb-12 sm:mb-16 lg:mb-20">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-center mb-8 sm:mb-10 lg:mb-12 bg-gradient-to-r from-[#2d4d31] to-[#d36c3c] bg-clip-text text-transparent">
          Advisory Board
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
          {advisors.map((advisor) => (
            <div
              key={advisor.id}
              className="bg-gradient-to-br from-white to-[#fff5ea] rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center border-2 border-[#2d4d31]/10 hover:border-[#2d4d31]/30 transition-all duration-300 hover:-translate-y-1 shadow-md hover:shadow-lg"
            >
              <div className="relative w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 mx-auto mb-3 sm:mb-4 lg:mb-6 rounded-full overflow-hidden shadow-md border-3 border-[#2d4d31]/10 flex-shrink-0">
                <img
                  src={advisor.image}
                  alt={advisor.name}
                  loading="lazy"
                  className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                />
                {advisor.linkedin && (
                  <a
                    href={advisor.linkedin}
                    className="absolute bottom-1 right-1 w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-[#d36c3c] to-[#b85a32] text-white rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:bg-gradient-to-br hover:from-[#2d4d31] hover:to-[#1f3322] shadow-md"
                    aria-label={`${advisor.name} LinkedIn profile`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 20 20"
                      fill="none"
                      className="sm:w-4 sm:h-4"
                    >
                      <path
                        d="M16.25 2.5H3.75C3.06 2.5 2.5 3.06 2.5 3.75V16.25C2.5 16.94 3.06 17.5 3.75 17.5H16.25C16.94 17.5 17.5 16.94 17.5 16.25V3.75C17.5 3.06 16.94 2.5 16.25 2.5ZM7.5 15H5V8.75H7.5V15ZM6.25 7.69C5.56 7.69 5 7.13 5 6.44C5 5.75 5.56 5.19 6.25 5.19C6.94 5.19 7.5 5.75 7.5 6.44C7.5 7.13 6.94 7.69 6.25 7.69ZM15 15H12.5V11.94C12.5 11.06 12.5 9.94 11.25 9.94C10 9.94 9.81 10.88 9.81 11.88V15H7.31V8.75H9.69V9.81H9.72C10.06 9.19 10.81 8.56 11.94 8.56C14.44 8.56 15 10.19 15 12.31V15Z"
                        fill="currentColor"
                      />
                    </svg>
                  </a>
                )}
              </div>

              <div className="flex flex-col items-center text-center">
                <h3 className="text-sm sm:text-base lg:text-lg font-bold text-black mb-1 sm:mb-2 leading-tight">
                  {advisor.name}
                </h3>
                <p className="text-xs sm:text-sm font-semibold text-[#d36c3c] mb-1 sm:mb-2">
                  {advisor.position}
                </p>
                <p className="text-xs sm:text-sm text-black opacity-80 leading-relaxed">
                  {advisor.company}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdvisoryBoard;
