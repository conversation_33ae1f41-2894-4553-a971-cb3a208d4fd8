const OurStory = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 my-12 sm:my-16 lg:my-20">
      <div className="bg-theme-alt-bg/50 rounded-xl sm:rounded-2xl p-6 sm:p-8 lg:p-12 text-center">
        <h3 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-theme-primary-text mb-6 sm:mb-8">
          Our Story
        </h3>
        <p className="text-sm sm:text-base lg:text-lg text-theme-primary-text leading-relaxed max-w-4xl mx-auto">
          Founded in early 2023, Agritram began with a clear truth: food prices
          were rising globally, yet the farmers producing it remained
          financially excluded. While inflation and poverty deepened, crop
          growers and cooperatives faced slow payments, opaque pricing, and
          systemic inequality. We saw a broken system and built a decentralised
          trade and finance platform to fix it. Agritram connects farmers,
          traders, and manufacturers through instant payments, traceable
          commodities, and smart contracts. We're not just digitising trade,
          we're creating financial access where it's needed most.
        </p>
      </div>
    </div>
  );
};

export default OurStory;
