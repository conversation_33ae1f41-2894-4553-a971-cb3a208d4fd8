/*
  # Create pitch_deck_requests table

  1. New Tables
    - `pitch_deck_requests`
      - `id` (uuid, primary key)
      - `company_name` (text, required)
      - `contact_name` (text, required)
      - `email` (text, required)
      - `phone` (text, optional)
      - `partner_type` (text, required)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `pitch_deck_requests` table
    - Add policies for anonymous inserts/updates and service role access
    - Add trigger for automatic updated_at updates
*/

CREATE TABLE IF NOT EXISTS pitch_deck_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  company_name text NOT NULL,
  contact_name text NOT NULL,
  email text NOT NULL,
  phone text,
  partner_type text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE pitch_deck_requests ENABLE ROW LEVEL SECURITY;

-- Create policies for access control
CREATE POLICY "Allow anonymous inserts for pitch deck requests"
  ON pitch_deck_requests
  FOR INSERT
  TO anon
  WITH CHECK (true);

CREATE POLICY "Allow anonymous updates for pitch deck requests"
  ON pitch_deck_requests
  FOR UPDATE
  TO anon
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Allow anonymous selects for pitch deck requests"
  ON pitch_deck_requests
  FOR SELECT
  TO anon
  USING (true);

CREATE POLICY "Service role can manage all pitch deck requests"
  ON pitch_deck_requests
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can manage pitch deck requests"
  ON pitch_deck_requests
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Add index for faster email lookups
CREATE INDEX IF NOT EXISTS idx_pitch_deck_requests_email ON pitch_deck_requests (email);

-- Create trigger to call the function on update (reusing existing function)
CREATE TRIGGER update_pitch_deck_requests_updated_at
  BEFORE UPDATE ON pitch_deck_requests
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();