import "./App.css";
import { Routes, Route, useLocation } from "react-router-dom";
import { useEffect } from "react";
import LandingPage from "./pages/LandingPage";
import NotFoundPage from "./pages/NotFoundPage";
import ServerErrorPage from "./pages/ServerErrorPage";
import PitchDeck from "./pages/PitchDeck";
import AboutUs from "./pages/AboutUs";
import TermsOfService from "./pages/TermsOfService";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import CookiePolicy from "./pages/CookiePolicy";
import Disclaimer from "./pages/Disclaimer";
import CookieBanner from "./components/CookieBanner";
import { initializeDataLayer, trackPageView } from "./utils/gtm";
import { getCookieConsent, type CookieConsent } from "./utils/cookies";

function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);

    // Track page views for analytics (only if consent given)
    const consent = getCookieConsent();
    if (consent?.analytics) {
      trackPageView(window.location.href, document.title);
    }
  }, [pathname]);

  return null;
}

function App() {
  useEffect(() => {
    // Initialize GTM data layer
    initializeDataLayer();
  }, []);

  const handleConsentChange = (consent: CookieConsent) => {
    // Handle consent changes if needed
    console.log("Cookie consent updated:", consent);
  };

  return (
    <>
      <ScrollToTop />
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/pitchdeck" element={<PitchDeck />} />
        <Route path="/about" element={<AboutUs />} />
        <Route path="/terms" element={<TermsOfService />} />
        <Route path="/privacy" element={<PrivacyPolicy />} />
        <Route path="/cookies" element={<CookiePolicy />} />
        <Route path="/disclaimer" element={<Disclaimer />} />
        <Route path="/404" element={<NotFoundPage />} />
        <Route path="/505" element={<ServerErrorPage />} />
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
      <CookieBanner
        onConsentChange={handleConsentChange}
        delayMs={30000} // 30 seconds delay
      />
    </>
  );
}

export default App;
