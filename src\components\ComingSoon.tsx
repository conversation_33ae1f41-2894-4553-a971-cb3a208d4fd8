import { <PERSON><PERSON><PERSON><PERSON>, Zap, Clock, Star, ArrowRight } from "lucide-react";
import NetworkBenefits from "./NetworkBenefits";

const ComingSoon = () => {
  const milestones = [
    {
      date: undefined,
      title: "Prototype",
      description: "Interactive design prototype of the Agritram platform",
      completed: true,
      current: false,
      dec: "Tested internally",
    },
    {
      date: undefined,
      title: "MVP Platform Launch",
      description:
        "Core tokenisation, smart contract settlement, and trade infrastructure",
      completed: false,
      current: true,
      dec: "Currently under development",
    },
    {
      date: undefined,
      title: "Pilot",
      description:
        "Live trades with real farmer, trader, and manufacturer onboarding",
      completed: false,
      current: false,
      dec: "Planned for rollout",
    },
    {
      date: undefined,
      title: "Global Product Rollout",
      description: "Expansion to support across Africa, UK & EU",
      completed: false,
      current: false,
      dec: "Planned for future launch",
    },
  ];

  return (
    <section
      id="coming-soon"
      className="py-12 sm:py-16 lg:py-20 bg-theme-alt-bg relative overflow-hidden"
    >
      <div className="max-w-7xl mx-auto mb-12 sm:mb-16 px-4 sm:px-6 lg:px-8">
        {/* Section Title */}
        <div className="text-center">
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-theme-primary-text mb-4 sm:mb-6 leading-tight">
            Next-Gen Platform
            <span className="block gradient-text">Coming Soon</span>
          </h2>
          <p className="text-lg sm:text-xl text-primary-text max-w-3xl mx-auto leading-relaxed px-2">
            We're building the most advanced agricultural technology platform.
            Be the first to experience revolutionary commodities trading that
            will transform your operations.
          </p>
        </div>
      </div>

      {/* Development Roadmap */}
      <div className="mb-16 px-4 sm:px-6 lg:px-8">
        {/* <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Development Roadmap</h3> */}
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Timeline Line - Responsive positioning */}
            <div className="absolute left-4 sm:left-8 top-0 bottom-0 w-0.5 bg-slate-300"></div>
            {/* Progress Line for Completed Milestones */}
            <div
              className="absolute left-4 sm:left-8 top-0 w-0.5 bg-gradient-to-b from-emerald-500 to-emerald-600 transition-all duration-1000 ease-out"
              style={{
                height: `${
                  (milestones.filter((m) => m.completed).length /
                    milestones.length) *
                  100
                }%`,
              }}
            ></div>
            {/* Current Progress Line */}
            <div
              className="absolute left-4 sm:left-8 w-0.5 bg-gradient-to-b from-amber-400 to-orange-500 transition-all duration-1000 ease-out"
              style={{
                top: `${
                  (milestones.filter((m) => m.completed).length /
                    milestones.length) *
                  100
                }%`,
                height: `${
                  milestones.some((m) => m.current)
                    ? (1 / milestones.length) * 50
                    : 0
                }%`,
              }}
            ></div>

            <div className="space-y-6 sm:space-y-8">
              {milestones.map((milestone, index) => (
                <div
                  key={index}
                  className={`relative flex items-start transition-all duration-500 animate-slide-in`}
                >
                  {/* Timeline Dot - Responsive sizing */}
                  <div
                    className={`relative z-10 w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center transition-all duration-300 group animate-glow flex-shrink-0 ${
                      milestone.completed
                        ? "bg-gradient-to-br from-emerald-500 to-emerald-700 shadow-lg shadow-emerald-500/30 ring-2 sm:ring-4 ring-emerald-100"
                        : milestone.current
                          ? "bg-gradient-to-br from-amber-400 to-orange-500 shadow-lg shadow-amber-500/40 ring-2 sm:ring-4 ring-amber-100"
                          : "bg-gradient-to-br from-blue-400 to-blue-500 shadow-lg shadow-blue-500/30 ring-2 sm:ring-4 ring-blue-100"
                    }`}
                  >
                    {milestone.completed ? (
                      <CheckCircle className="h-6 w-6 sm:h-8 sm:w-8 text-white drop-shadow-sm" />
                    ) : milestone.current ? (
                      <Zap className="h-6 w-6 sm:h-8 sm:w-8 text-white drop-shadow-sm" />
                    ) : (
                      <Star className="h-6 w-6 sm:h-8 sm:w-8 text-white drop-shadow-sm" />
                    )}
                  </div>

                  {/* Content - Responsive spacing */}
                  <div className="ml-4 sm:ml-8 flex-1 min-w-0">
                    <div
                      className={`rounded-xl sm:rounded-2xl p-4 sm:p-6 transition-all duration-300 group ${
                        milestone.completed
                          ? "bg-gradient-to-r from-emerald-50 to-green-50 border-2 border-emerald-200 shadow-md hover:shadow-lg"
                          : milestone.current
                            ? "bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 shadow-md hover:shadow-lg ring-2 ring-amber-100"
                            : "bg-gradient-to-r from-slate-50 to-blue-50 border-2 border-slate-200 shadow-md hover:shadow-lg ring-2 ring-blue-100"
                      }`}
                    >
                      {/* Mobile-first layout: stack title and badges vertically on small screens */}
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-2 gap-2 sm:gap-3">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 min-w-0">
                          <h4
                            className={`text-base sm:text-lg font-bold transition-colors duration-300 ${
                              milestone.completed
                                ? "text-emerald-800"
                                : milestone.current
                                  ? "text-amber-800"
                                  : "text-blue-800"
                            }`}
                          >
                            {milestone.title}
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {milestone.completed && (
                              <span className="inline-flex items-center px-2 py-1 sm:px-2.5 sm:py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200">
                                ✓ Completed
                              </span>
                            )}
                            {milestone.current && (
                              <span className="inline-flex items-center px-2 py-1 sm:px-2.5 sm:py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200">
                                <Clock className="w-3 h-3 mr-1" />
                                In Progress
                              </span>
                            )}
                            {!milestone.completed && !milestone.current && (
                              <span className="inline-flex items-center px-2 py-1 sm:px-2.5 sm:py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-600 border border-blue-200">
                                <ArrowRight className="w-3 h-3 mr-1" />
                                Coming Soon
                              </span>
                            )}
                          </div>
                        </div>
                        {milestone.date && (
                          <span
                            className={`text-sm font-medium transition-colors duration-300 self-start sm:self-auto ${
                              milestone.completed
                                ? "text-emerald-700"
                                : milestone.current
                                  ? "text-amber-700"
                                  : "text-blue-700"
                            }`}
                          >
                            {milestone.date}
                          </span>
                        )}
                      </div>
                      <p
                        className={`text-sm sm:text-base transition-colors duration-300 leading-relaxed ${
                          milestone.completed
                            ? "text-emerald-700"
                            : milestone.current
                              ? "text-amber-700"
                              : "text-blue-700"
                        }`}
                      >
                        {milestone.description}
                      </p>
                      {milestone.completed && (
                        <div className="mt-3 flex items-center text-xs sm:text-sm text-emerald-600">
                          <div className="w-2 h-2 bg-emerald-500 rounded-full mr-2 animate-pulse flex-shrink-0"></div>
                          <span className="break-words">{milestone.dec}</span>
                        </div>
                      )}
                      {milestone.current && (
                        <div className="mt-3 flex items-center text-xs sm:text-sm text-amber-600">
                          <div className="w-2 h-2 bg-amber-500 rounded-full mr-2 animate-pulse flex-shrink-0"></div>
                          <span className="break-words">{milestone.dec}</span>
                        </div>
                      )}
                      {!milestone.completed && !milestone.current && (
                        <div className="mt-3 flex items-center text-xs sm:text-sm text-blue-600">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse flex-shrink-0"></div>
                          <span className="break-words">{milestone.dec}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <NetworkBenefits />
    </section>
  );
};

export default ComingSoon;
