import React, { useState, useEffect } from "react";
import { Send, X, CheckCircle } from "lucide-react";
import { supabase } from "../lib/supabase";
import { Button } from "./ui";
// @ts-expect-error: No type definitions for emailService.cjs
import { sendPartnershipEmail } from "../utils/emailService";

interface PartnershipModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialPartnerType: string;
}

const PartnershipModal: React.FC<PartnershipModalProps> = ({
  isOpen,
  onClose,
  initialPartnerType,
}) => {
  const [formData, setFormData] = useState({
    companyName: "",
    contactName: "",
    email: "",
    phone: "",
    partnerType: initialPartnerType,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [countdown, setCountdown] = useState(60);

  useEffect(() => {
    setFormData((prev) => ({ ...prev, partnerType: initialPartnerType }));
    setError(null);
  }, [initialPartnerType, isOpen]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setIsSubmitted(false);
      setError(null);
      setCountdown(60);
    }
  }, [isOpen]);

  // Countdown timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (isSubmitted && countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else if (isSubmitted && countdown === 0) {
      // Auto-close when countdown reaches 0
      setFormData({
        companyName: "",
        contactName: "",
        email: "",
        phone: "",
        partnerType: "",
      });
      setIsSubmitted(false);
      setCountdown(60);
      onClose();
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isSubmitted, countdown, onClose]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsSubmitting(true);
    setError(null);

    try {
      // Check if request already exists (by email)
      const { data: existingRequest, error: selectError } = await supabase
        .from("pitch_deck_requests")
        .select("*")
        .eq("email", formData.email)
        .maybeSingle();

      if (selectError) {
        console.error("Supabase select error:", selectError);
        throw new Error("Database query failed");
      }

      if (existingRequest) {
        // Update existing request: increment visits_count
        // Add new partner type to array if not already present
        const currentPartnerTypes = existingRequest.partner_type || [];
        const updatedPartnerTypes = currentPartnerTypes.includes(
          formData.partnerType
        )
          ? currentPartnerTypes
          : [...currentPartnerTypes, formData.partnerType];

        const { error: updateError } = await supabase
          .from("pitch_deck_requests")
          .update({
            visits_count: existingRequest.visits_count + 1,
            company_name: formData.companyName, // Update in case it changed
            contact_name: formData.contactName, // Update in case it changed
            phone: formData.phone || null, // Update phone
            partner_type: updatedPartnerTypes, // Add new partner type to array
          })
          .eq("id", existingRequest.id)
          .select()
          .single();

        if (updateError) {
          console.error("Supabase update error:", updateError);
          throw new Error("Failed to update request data");
        }
      } else {
        // Insert new request
        const { error: insertError } = await supabase
          .from("pitch_deck_requests")
          .insert({
            company_name: formData.companyName,
            contact_name: formData.contactName,
            email: formData.email,
            phone: formData.phone || null,
            partner_type: [formData.partnerType], // Store as array
            visits_count: 1,
          })
          .select()
          .single();

        if (insertError) {
          console.error("Supabase insert error:", insertError);
          throw new Error("Failed to insert new request data");
        }
      }

      // Send automated email
      try {
        await sendPartnershipEmail(formData);
        console.log("Thank you email sent successfully");
      } catch (emailError) {
        console.error("Failed to send thank you email:", emailError);
      }

      // Success - show success message and close modal
      // Trigger multiple PDF downloads
      try {
        const downloads = [
          {
            url: "/Agritram-Pitch-Deck-2025.pdf", // Place your PDF in the public folder
            filename: "Agritram-Pitch-Deck-2025.pdf",
          },
          {
            url: "/Agritram-Whitepaper.pdf",
            filename: "Agritram-Whitepaper.pdf",
          },
        ];

        // Download all files
        const downloadPromises = downloads.map(async ({ url, filename }) => {
          try {
            // For local files, use fetch approach
            const response = await fetch(url);

            if (!response.ok) {
              throw new Error(
                `Failed to fetch ${filename}: ${response.status}`
              );
            }

            // Get the PDF as a blob
            const blob = await response.blob();

            // Create a blob URL
            const blobUrl = window.URL.createObjectURL(blob);

            // Create download link
            const link = document.createElement("a");
            link.href = blobUrl;
            link.download = filename;
            link.style.display = "none";

            // Trigger download
            document.body.appendChild(link);
            link.click();

            // Clean up
            document.body.removeChild(link);
            window.URL.revokeObjectURL(blobUrl);

            return { success: true, filename };
          } catch (error) {
            console.error(`Download failed for ${filename}:`, error);
            return { success: false, filename, error };
          }
        });

        // Wait for all downloads to complete
        const results = await Promise.all(downloadPromises);

        // Check if any downloads failed
        const failedDownloads = results.filter((result) => !result.success);

        if (failedDownloads.length > 0) {
          const failedFiles = failedDownloads
            .map((result) => result.filename)
            .join(", ");
          setError(
            `Some downloads failed: ${failedFiles}. Please try again or contact support.`
          );
          return; // Don't proceed with success state if any download fails
        }
      } catch (downloadError) {
        console.error("PDF downloads failed:", downloadError);
        setError(
          "Failed to download files. Please try again or contact support."
        );
        return; // Don't proceed with success state if download fails
      }

      setIsSubmitted(true);
      setCountdown(60); // Start 60-second countdown
    } catch (err) {
      console.error("Partnership submission error:", err);
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  // Success state
  if (isSubmitted) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4 transition-opacity duration-300">
        <div className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 shadow-lg w-full max-w-md text-center transform transition-all duration-300 relative max-h-[90vh] overflow-y-auto">
          <button
            onClick={() => {
              setFormData({
                companyName: "",
                contactName: "",
                email: "",
                phone: "",
                partnerType: "",
              });
              setIsSubmitted(false);
              setCountdown(60);
              onClose();
            }}
            className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-500 hover:text-gray-800"
            aria-label="Close success modal"
          >
            <X size={20} className="sm:w-6 sm:h-6" />
          </button>
          <div className="bg-emerald-100 w-16 h-16 sm:w-20 sm:h-20 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
            <CheckCircle className="h-8 w-8 sm:h-10 sm:w-10 text-emerald-600" />
          </div>
          <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
            Thank You for Your Interest!
          </h3>
          <p className="text-base sm:text-lg text-gray-600 mb-4 sm:mb-6 px-2 leading-relaxed">
            Your partnership inquiry has been successfully submitted and the
            requested documents have been downloaded to your device. Our
            partnership team will review your information and contact you within
            24 hours.
          </p>
          <div className="bg-emerald-50 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
            <p className="text-emerald-800 font-medium text-xs sm:text-sm">
              We will reach out to you at{" "}
              <strong className="break-all">{formData.email}</strong> to discuss
              potential collaboration opportunities.
            </p>
          </div>
          <div className="bg-blue-50 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
            <p className="text-blue-800 font-medium text-xs sm:text-sm">
              This window will automatically close in{" "}
              <strong>{countdown} seconds</strong>
            </p>
          </div>
          <button
            onClick={() => {
              setFormData({
                companyName: "",
                contactName: "",
                email: "",
                phone: "",
                partnerType: "",
              });
              setIsSubmitted(false);
              setCountdown(60);
              onClose();
            }}
            className="mt-4 bg-emerald-600 text-white py-2 px-6 rounded-lg hover:bg-emerald-700 transition-colors duration-200 font-semibold"
          >
            Close Now
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4 transition-opacity duration-300">
      <div className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 shadow-lg w-full max-w-md sm:max-w-lg lg:max-w-2xl max-h-[90vh] overflow-y-auto relative transform transition-all duration-300">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-500 hover:text-gray-800"
          disabled={isSubmitting}
        >
          <X size={20} className="sm:w-6 sm:h-6" />
        </button>
        <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 pr-8">
          Partnership Inquiry Form
        </h3>
        <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">
          Tell us about your organization and partnership interests. We'll get
          back to you with relevant information and next steps.
        </p>
        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4">
              <p className="text-red-700 text-xs sm:text-sm">{error}</p>
            </div>
          )}

          <div className="grid sm:grid-cols-2 gap-3 sm:gap-4">
            <div>
              <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                Company Name *
              </label>
              <input
                type="text"
                name="companyName"
                value={formData.companyName}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 text-sm sm:text-base"
                placeholder="Your company name"
              />
            </div>
            <div>
              <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                Contact Name *
              </label>
              <input
                type="text"
                name="contactName"
                value={formData.contactName}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 text-sm sm:text-base"
                placeholder="Your full name"
              />
            </div>
          </div>

          <div className="grid sm:grid-cols-2 gap-3 sm:gap-4">
            <div>
              <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                Email Address *
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 text-sm sm:text-base"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                Phone Number
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                disabled={isSubmitting}
                className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 text-sm sm:text-base"
                placeholder="+****************"
              />
            </div>
          </div>

          <div>
            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
              Partnership Type *
            </label>
            <select
              name="partnerType"
              value={formData.partnerType}
              onChange={handleInputChange}
              required
              disabled={isSubmitting}
              className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 text-sm sm:text-base"
            >
              <option value="">Select partnership type</option>
              <option value="corporate">Corporates</option>
              <option value="investor">Investors</option>
              <option value="angel-Investor">Angel Investor</option>
              <option value="corporate-and-institutional-banking">
                Corporate and Institutional Banking (CIB)
              </option>
              <option value="other">Other</option>
            </select>
          </div>

          {isSubmitting ? (
            <Button
              variant="primary"
              size="lg"
              isLoading
              fullWidth
              className="py-3 sm:py-4"
            >
              Submitting...
            </Button>
          ) : (
            <Button
              variant="primary"
              size="lg"
              type="submit"
              fullWidth
              icon={Send}
              className="py-3 sm:py-4"
            >
              Submit Partnership Inquiry
            </Button>
          )}

          <p className="text-xs sm:text-sm text-gray-500 text-center leading-relaxed px-2 mt-4">
            By submitting this form, you agree to our{" "}
            <a href="/privacy" className="underline hover:no-underline">
              Privacy Policy
            </a>
            . We'll use your information to process your partnership inquiry and
            send you relevant documents.
          </p>
        </form>
      </div>
    </div>
  );
};

export default PartnershipModal;

// Add this to your main CSS file for the animation
/*
@keyframes scale-in {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-scale-in {
  animation: scale-in 0.2s ease-out forwards;
}
*/
