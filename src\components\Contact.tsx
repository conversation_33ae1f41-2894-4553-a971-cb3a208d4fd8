import React, { useState } from "react";
import {
  Mail,
  MapPin,
  Send,
  MessageCircle,
  Clock,
  CheckCircle,
} from "lucide-react";
import { supabase } from "../lib/supabase";
import { Button } from "./ui";
// @ts-expect-error: No type definitions for emailService.cjs
import { sendContactEmail } from "../utils/emailService";

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successCountdown, setSuccessCountdown] = useState<number | null>(null);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsSubmitting(true);
    setError(null);

    try {
      const subjectLabel =
        supportOptions.find((option) => option.value === formData.subject)
          ?.label || formData.subject;
      // Always create a new contact message record
      const { error: insertError } = await supabase
        .from("contact_messages")
        .insert({
          full_name: formData.name,
          email: formData.email,
          subject: subjectLabel,
          message: formData.message,
        })
        .select()
        .single();

      if (insertError) {
        console.error("Supabase insert error:", insertError);
        throw new Error("Failed to insert new contact data");
      }

      // Send automated thank you email
      try {
        await sendContactEmail({
          name: formData.name,
          email: formData.email,
          subject: subjectLabel,
          message: formData.message,
        });
        console.log("Thank you email sent successfully");
      } catch (emailError) {
        console.error("Failed to send thank you email:", emailError);
        // Don't fail the form submission if email fails
      }

      // Success - show success message
      setIsSubmitted(true);
      setSuccessCountdown(60); // 60 seconds

      // Countdown timer
      const interval = setInterval(() => {
        setSuccessCountdown((prev) => {
          if (prev === null) return null;
          if (prev <= 1) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      // Reset form after 60 seconds
      setTimeout(() => {
        setIsSubmitted(false);
        setFormData({
          name: "",
          email: "",
          subject: "",
          message: "",
        });
        setSuccessCountdown(null);
        clearInterval(interval);
      }, 60000);
    } catch (err) {
      console.error("Contact form submission error:", err);
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: Mail,
      title: "Email Us",
      detail: "<EMAIL>",
      description: "Send us an email and we'll respond within 24 hours",
    },
    {
      icon: MapPin,
      title: "Visit Us",
      detail: "London, United Kingdom",
      description: "Schedule a meeting at our headquarters",
    },
  ];

  const supportOptions = [
    { value: "general", label: "General Inquiry" },
    { value: "partnership", label: "Partnership Opportunities" },
    { value: "technical", label: "Technical Support" },
    { value: "investment", label: "Investment Inquiry" },
    { value: "media", label: "Media & Press" },
    { value: "other", label: "Other" },
  ];

  if (isSubmitted) {
    return (
      <section id="contact" className="py-12 sm:py-16 lg:py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-emerald-50 rounded-xl sm:rounded-2xl p-6 sm:p-8 lg:p-12">
            <div className="bg-emerald-100 w-16 h-16 sm:w-20 sm:h-20 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
              <CheckCircle className="h-8 w-8 sm:h-10 sm:w-10 text-emerald-600" />
            </div>
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
              Message Sent Successfully!
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 px-2">
              Thank you for reaching out to us. We'll get back to you within 24
              hours.
            </p>
            <div className="bg-white rounded-lg p-4 sm:p-6 shadow-sm">
              <p className="text-sm sm:text-base text-emerald-800 font-medium">
                Our team will review your message and respond to you at{" "}
                <span className="break-all">{formData.email}</span>
              </p>
              {successCountdown !== null && (
                <p className="text-xs sm:text-sm text-gray-500 mt-3 sm:mt-4">
                  This message will close in {successCountdown} second
                  {successCountdown !== 1 ? "s" : ""}.
                </p>
              )}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="contact" className="py-12 sm:py-16 lg:py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
            Get in Touch
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto px-2">
            Have questions about Agritram? We'd love to hear from you. Send us a
            message and we'll respond as soon as possible.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6 sm:gap-8">
          {/* Contact Information */}
          <div className="space-y-6 sm:space-y-8 order-2 lg:order-1">
            <div>
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 text-center lg:text-left">
                Contact Information
              </h3>
              <div className="space-y-4 sm:space-y-6">
                {contactInfo.map((info, index) => {
                  const IconComponent = info.icon;
                  return (
                    <div
                      key={index}
                      className="flex items-start space-x-3 sm:space-x-4"
                    >
                      <div className="bg-emerald-100 p-2 sm:p-3 rounded-lg flex-shrink-0">
                        <IconComponent className="h-5 w-5 sm:h-6 sm:w-6 text-emerald-600" />
                      </div>
                      <div>
                        <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1">
                          {info.title}
                        </h4>
                        <p className="text-sm sm:text-base text-emerald-600 font-medium mb-1 break-all">
                          {info.detail}
                        </p>
                        <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                          {info.description}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Response Time */}
            <div className="bg-emerald-50 rounded-xl sm:rounded-2xl p-4 sm:p-6">
              <div className="flex items-center mb-3 sm:mb-4">
                <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-emerald-600 mr-2 sm:mr-3" />
                <h4 className="text-sm sm:text-base font-semibold text-gray-900">
                  Response Time
                </h4>
              </div>
              <div className="space-y-1 sm:space-y-2 text-xs sm:text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Email inquiries:</span>
                  <span className="font-medium text-gray-900">
                    {"24 - 48 hours"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Partnership inquiries:</span>
                  <span className="font-medium text-gray-900">
                    {"24 - 48 hours"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2 order-1 lg:order-2">
            <div className="bg-gray-50 rounded-xl sm:rounded-2xl p-6 sm:p-8">
              <div className="flex items-center mb-4 sm:mb-6 justify-center lg:justify-start">
                <MessageCircle className="h-5 w-5 sm:h-6 sm:w-6 text-emerald-600 mr-2 sm:mr-3" />
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900">
                  Send us a Message
                </h3>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4">
                    <p className="text-red-700 text-xs sm:text-sm">{error}</p>
                  </div>
                )}

                <div className="grid sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      disabled={isSubmitting}
                      className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 bg-white text-sm sm:text-base"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      disabled={isSubmitting}
                      className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 bg-white text-sm sm:text-base"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                    Subject *
                  </label>
                  <select
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 bg-white text-sm sm:text-base"
                  >
                    <option value="">Select a subject</option>
                    {supportOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                    Message
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                    rows={4}
                    className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200 bg-white text-sm sm:text-base resize-none sm:resize-y"
                    placeholder="Please describe your inquiry in detail..."
                  />
                </div>
                {isSubmitting ? (
                  <Button
                    variant="primary"
                    size="lg"
                    isLoading
                    fullWidth
                    className="py-3 sm:py-4"
                  >
                    Sending...
                  </Button>
                ) : (
                  <Button
                    variant="primary"
                    size="lg"
                    type="submit"
                    fullWidth
                    icon={Send}
                    className="py-3 sm:py-4"
                  >
                    Send Message
                  </Button>
                )}

                <p className="text-xs sm:text-sm text-gray-500 text-center leading-relaxed px-2">
                  We respect your privacy. Your information will only be used to
                  respond to your inquiry.
                </p>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
