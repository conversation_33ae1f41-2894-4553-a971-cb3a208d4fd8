import React, { useState, useEffect } from "react";
import { X, Download, User, Mail, Building, CheckCircle } from "lucide-react";
import { Button } from "./ui";
import { supabase } from "../lib/supabase";
// @ts-expect-error: No type definitions for emailService.cjs
import { sendWhitepaperEmail } from "../utils/emailService";

interface FormData {
  fullName: string;
  email: string;
  company: string;
}

interface DownloadFormModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const DownloadFormModal: React.FC<DownloadFormModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [formData, setFormData] = useState<FormData>({
    fullName: "",
    email: "",
    company: "",
  });
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [generalError, setGeneralError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [secondsLeft, setSecondsLeft] = useState(60); // Timer for success modal

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setIsSubmitted(false);
      setGeneralError(null);
      setSecondsLeft(60); // Reset timer
    }
  }, [isOpen]);

  // Countdown timer effect for success modal
  useEffect(() => {
    if (isSubmitted) {
      setSecondsLeft(60); // Start at 60 seconds
      const interval = setInterval(() => {
        setSecondsLeft((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            onClose();
            setIsSubmitted(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isSubmitted, onClose]);

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.fullName.trim()) newErrors.fullName = "Full name is required";
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
    if (!formData.company.trim())
      newErrors.company = "Company name is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    setGeneralError(null);

    try {
      // Check if lead already exists
      const { data: existingLead, error: selectError } = await supabase
        .from("whitepaper_leads")
        .select("*")
        .eq("email", formData.email)
        .maybeSingle();

      if (selectError) {
        console.error("Supabase select error:", selectError);
        throw new Error("Database query failed");
      }

      if (existingLead) {
        // Update existing lead: increment visits_count
        const { error: updateError } = await supabase
          .from("whitepaper_leads")
          .update({
            visits_count: existingLead.visits_count + 1,
            full_name: formData.fullName, // Update name in case it changed
            company: formData.company, // Update company in case it changed
          })
          .eq("id", existingLead.id)
          .select()
          .single();

        if (updateError) {
          console.error("Supabase update error:", updateError);
          throw new Error("Failed to update lead data");
        }
      } else {
        // Insert new lead
        const { error: insertError } = await supabase
          .from("whitepaper_leads")
          .insert({
            full_name: formData.fullName,
            email: formData.email,
            company: formData.company,
            visits_count: 1,
          })
          .select()
          .single();

        if (insertError) {
          console.error("Supabase insert error:", insertError);
          throw new Error("Failed to insert new lead data");
        }
      }

      // Then proceed with the download
      // Success - show success message and close modal
      // Trigger multiple PDF downloads
      try {
        const downloads = [
          {
            url: "/Agritram-Whitepaper.pdf",
            filename: "Agritram-Whitepaper.pdf",
          },
        ];

        // Download all files
        const downloadPromises = downloads.map(async ({ url, filename }) => {
          try {
            // For local files, use fetch approach
            const response = await fetch(url);

            if (!response.ok) {
              throw new Error(
                `Failed to fetch ${filename}: ${response.status}`
              );
            }

            // Get the PDF as a blob
            const blob = await response.blob();

            // Create a blob URL
            const blobUrl = window.URL.createObjectURL(blob);

            // Create download link
            const link = document.createElement("a");
            link.href = blobUrl;
            link.download = filename;
            link.style.display = "none";

            // Trigger download
            document.body.appendChild(link);
            link.click();

            // Clean up
            document.body.removeChild(link);
            window.URL.revokeObjectURL(blobUrl);

            return { success: true, filename };
          } catch (error) {
            console.error(`Download failed for ${filename}:`, error);
            return { success: false, filename, error };
          }
        });

        // Wait for all downloads to complete
        const results = await Promise.all(downloadPromises);

        // Check if any downloads failed
        const failedDownloads = results.filter((result) => !result.success);

        if (failedDownloads.length > 0) {
          const failedFiles = failedDownloads
            .map((result) => result.filename)
            .join(", ");
          setGeneralError(
            `Some downloads failed: ${failedFiles}. Please try again or contact support.`
          );
          return; // Don't proceed with success state if any download fails
        }
      } catch (downloadError) {
        console.error("PDF downloads failed:", downloadError);
        setGeneralError(
          "Failed to download files. Please try again or contact support."
        );
        return; // Don't proceed with success state if download fails
      }

      // Send automated email
      try {
        await sendWhitepaperEmail(formData);
        console.log("Thank you email sent successfully");
      } catch (emailError) {
        console.error("Failed to send thank you email:", emailError);
      }

      // Success - show success message
      setIsSubmitted(true);

      // Reset form and close modal after 60 seconds
      setTimeout(() => {
        setFormData({ fullName: "", email: "", company: "" });
        setErrors({});
        setIsSubmitted(false);
        onClose();
      }, 60000);
    } catch (error) {
      console.error("White paper download error:", error);
      // Show error to user
      setGeneralError(
        error instanceof Error
          ? error.message
          : "Failed to process white paper download. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({ fullName: "", email: "", company: "" });
      setErrors({});
      setGeneralError(null);
      setIsSubmitted(false);
      onClose();
    }
  };

  if (!isOpen) return null;

  // Success state
  if (isSubmitted) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4 transition-opacity duration-300">
        <div className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 shadow-lg w-full max-w-md text-center transform transition-all duration-300 relative">
          <button
            onClick={handleClose}
            className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-500 hover:text-gray-800"
            aria-label="Close success modal"
          >
            <X size={20} className="sm:w-6 sm:h-6" />
          </button>
          <div className="bg-emerald-100 w-16 h-16 sm:w-20 sm:h-20 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
            <CheckCircle className="h-8 w-8 sm:h-10 sm:w-10 text-emerald-600" />
          </div>
          <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
            Download Started Successfully!
          </h3>
          <p className="text-base sm:text-lg text-gray-600 mb-4 sm:mb-6 px-2">
            Your white paper download has been initiated. The file should start
            downloading shortly.
          </p>
          <div className="bg-emerald-50 rounded-lg p-3 sm:p-4 mb-4">
            <p className="text-emerald-800 font-medium text-sm">
              Thank you for your interest in Agritram.
            </p>
            <p className="text-emerald-800 font-medium text-xs sm:text-sm mt-2">
              This window will close automatically in {secondsLeft} second
              {secondsLeft !== 1 ? "s" : ""}.
            </p>
          </div>
          <Button
            variant="primary"
            size="md"
            onClick={handleClose}
            className="w-full sm:w-auto"
          >
            Close
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-md sm:max-w-lg lg:max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2 sm:space-x-3 min-w-0">
            <div
              className="inline-flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-full flex-shrink-0"
              style={{ backgroundColor: "#2d4d31" }}
            >
              <Download className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
            <div className="min-w-0">
              <h3 className="text-base sm:text-lg font-semibold text-black truncate">
                Download White Paper
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 hidden sm:block">
                Enter your details to download the white paper
              </p>
            </div>
          </div>

          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors disabled:opacity-50 flex-shrink-0"
          >
            <X className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form
          onSubmit={handleSubmit}
          className="p-4 sm:p-6 space-y-3 sm:space-y-4"
        >
          <div>
            <label
              htmlFor="download-fullName"
              className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2"
            >
              Full Name *
            </label>
            <div className="relative">
              <User className="absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
              <input
                type="text"
                id="download-fullName"
                value={formData.fullName}
                onChange={(e) => handleInputChange("fullName", e.target.value)}
                className={`w-full pl-8 sm:pl-10 pr-3 sm:pr-4 py-2 sm:py-3 border rounded-lg focus:ring-2 focus:ring-[#2d4d31] text-sm sm:text-base ${
                  errors.fullName ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="John Smith"
                disabled={isSubmitting}
              />
            </div>
            {errors.fullName && (
              <p className="text-red-500 text-xs sm:text-sm mt-1">
                {errors.fullName}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="download-email"
              className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2"
            >
              Email Address *
            </label>
            <div className="relative">
              <Mail className="absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
              <input
                type="email"
                id="download-email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={`w-full pl-8 sm:pl-10 pr-3 sm:pr-4 py-2 sm:py-3 border rounded-lg focus:ring-2 focus:ring-[#2d4d31] text-sm sm:text-base ${
                  errors.email ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="<EMAIL>"
                disabled={isSubmitting}
              />
            </div>
            {errors.email && (
              <p className="text-red-500 text-xs sm:text-sm mt-1">
                {errors.email}
              </p>
            )}
            {generalError && (
              <p className="text-red-500 text-xs sm:text-sm mt-2 text-center">
                {generalError}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="download-company"
              className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2"
            >
              Company Name *
            </label>
            <div className="relative">
              <Building className="absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
              <input
                type="text"
                id="download-company"
                value={formData.company}
                onChange={(e) => handleInputChange("company", e.target.value)}
                className={`w-full pl-8 sm:pl-10 pr-3 sm:pr-4 py-2 sm:py-3 border rounded-lg focus:ring-2 focus:ring-[#2d4d31] text-sm sm:text-base ${
                  errors.company ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Your Company"
                disabled={isSubmitting}
              />
            </div>
            {errors.company && (
              <p className="text-red-500 text-xs sm:text-sm mt-1">
                {errors.company}
              </p>
            )}
          </div>

          {/* Submit Button */}
          {isSubmitting ? (
            <Button
              variant="primary"
              size="lg"
              isLoading
              fullWidth
              className="py-3 sm:py-4"
            >
              Submitting...
            </Button>
          ) : (
            <Button
              variant="primary"
              size="lg"
              fullWidth
              type="submit"
              icon={Download}
              className="py-3 sm:py-4"
            >
              Download White Paper
            </Button>
          )}
        </form>

        {/* Footer */}
        <div className="px-4 sm:px-6 pb-4 sm:pb-6">
          <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
            <p className="text-xs sm:text-sm text-gray-600 text-center leading-relaxed">
              By downloading this white paper, you agree to our{" "}
              <a
                href="/privacy"
                className="underline hover:no-underline"
                style={{ color: "#2d4d31" }}
              >
                Privacy Policy
              </a>
              . Your information will be used to provide access to the white
              paper and send you the download link.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
