/*
  # Fix whitepaper leads RLS policies

  1. Security Updates
    - Drop existing restrictive policy
    - Add new policy allowing anonymous inserts and updates
    - Add policy for service role to manage all data
    - Ensure proper access for edge functions

  2. Changes
    - Allow anonymous users to insert new leads
    - Allow anonymous users to update existing leads (for visit counting)
    - Maintain service role access for full management
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Service role can manage whitepaper leads" ON whitepaper_leads;

-- Create new policies for proper access
CREATE POLICY "Allow anonymous inserts for whitepaper leads"
  ON whitepaper_leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

CREATE POLICY "Allow anonymous updates for whitepaper leads"
  ON whitepaper_leads
  FOR UPDATE
  TO anon
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Allow anonymous selects for whitepaper leads"
  ON whitepaper_leads
  FOR SELECT
  TO anon
  USING (true);

CREATE POLICY "Service role can manage all whitepaper leads"
  ON whitepaper_leads
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can manage whitepaper leads"
  ON whitepaper_leads
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);