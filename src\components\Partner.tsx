import React from "react";
import {
  Building2,
  Handshake,
  TrendingUp,
  Users,
  Mail,
  MapPin,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Button } from "./ui";

const Partner: React.FC = () => {
  const navigate = useNavigate();

  const partnerTypes = [
    {
      type: "Corporates",
      value: "corporate",
      icon: Building2,
      description:
        "Large corporations looking to integrate blockchain transparency into their supply chains",
      benefits: [
        "Brand protection",
        "Supply chain visibility",
        "ESG compliance",
        "Quality assurance",
      ],
    },
    {
      type: "Investors",
      value: "investor",
      icon: TrendingUp,
      description:
        "Investment firms and VCs interested in agricultural technology and blockchain solutions",
      benefits: [
        "Market growth potential",
        "Scalable technology",
        "Strong unit economics",
        "Experienced team",
      ],
    },
    {
      type: "Angel Investor",
      value: "angel-Investor",
      icon: Users,
      description:
        "Individuals providing capital and mentorship to promising agri-tech startups like Agritram.",
      benefits: [
        "High-growth potential in agri-tech",
        "Opportunity for significant equity returns",
        "Direct impact on food security and sustainability",
        "Joining a curated network of innovators",
      ],
    },
    {
      type: "Corporate and Institutional Banking (CIB)",
      value: "corporate-and-institutional-banking",
      icon: Handshake,
      description:
        "Financial institutions providing tailored banking services, including loans, trade finance, and risk management to the agricultural sector.",
      benefits: [
        "Access to a de-risked portfolio of agricultural SMEs",
        "Opportunity to finance transparent and traceable supply chains",
        "Expand footprint in the growing agri-tech sector",
        "New product development for commodity financing",
      ],
    },
  ];

  return (
    <section id="partner" className="py-12 sm:py-16 lg:py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
            Become a Strategic Partner
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto px-2">
            Join us in revolutionizing agricultural finance. We welcome
            corporates, investors, cooperatives, and NGOs who share our vision
            of transparent, equitable agriculture.
          </p>
        </div>

        {/* Partner Types */}
        <div className="grid lg:grid-cols-2 gap-6 sm:gap-8 mb-12 sm:mb-16">
          {partnerTypes.map((partner, index) => {
            const IconComponent = partner.icon;
            return (
              <div
                key={index}
                className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-4">
                  <div className="bg-emerald-100 p-2 sm:p-3 rounded-lg flex-shrink-0 self-center sm:self-start">
                    <IconComponent className="h-5 w-5 sm:h-6 sm:w-6 text-emerald-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex flex-col justify-between text-center sm:text-left">
                      <div>
                        <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-2 sm:mb-3">
                          {partner.type}
                        </h3>
                        <p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4 leading-relaxed">
                          {partner.description}
                        </p>
                        <div className="space-y-1 sm:space-y-2">
                          {partner.benefits.map((benefit, benefitIndex) => (
                            <div
                              key={benefitIndex}
                              className="flex items-center text-xs sm:text-sm text-gray-700 justify-center sm:justify-start"
                            >
                              <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-2 sm:mr-3 flex-shrink-0"></div>
                              <span className="leading-relaxed">{benefit}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className="mt-4 sm:mt-6">
                        <Button
                          variant="primary"
                          size="md"
                          onClick={() => {
                            navigate("/pitchdeck", {
                              state: { selectedPartnerType: partner.value },
                            });
                          }}
                          className="w-full sm:w-auto"
                        >
                          Partner with Us
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Partnership Benefits & Contact */}
        <div className="grid lg:grid-cols-2 gap-8 sm:gap-12 items-start">
          <div className="space-y-6 sm:space-y-8 order-2 lg:order-1">
            <div>
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 text-center lg:text-left">
                Why Partner With Agritram?
              </h3>
              <div className="space-y-4 sm:space-y-6">
                <div className="flex items-start space-x-3 sm:space-x-4">
                  <div className="bg-emerald-100 p-2 rounded-lg flex-shrink-0">
                    <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-emerald-600" />
                  </div>
                  <div>
                    <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1">
                      Market Leadership
                    </h4>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                      Be part of the next generation of agricultural finance
                      technology
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 sm:space-x-4">
                  <div className="bg-blue-100 p-2 rounded-lg flex-shrink-0">
                    <Users className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1">
                      Network Access
                    </h4>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                      Connect with our growing network of farmers, traders, and
                      manufacturers
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 sm:space-x-4">
                  <div className="bg-amber-100 p-2 rounded-lg flex-shrink-0">
                    <Handshake className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                  </div>
                  <div>
                    <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-1">
                      Strategic Collaboration
                    </h4>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                      Work closely with our team to shape the future of
                      agri-finance
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Contact Information */}
          <div className="bg-emerald-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 self-start order-1 lg:order-2">
            <h4 className="text-sm sm:text-base font-semibold text-gray-900 mb-3 sm:mb-4 text-center lg:text-left">
              Direct Contact
            </h4>
            <div className="space-y-2 sm:space-y-3">
              <div className="flex items-center justify-center lg:justify-start">
                <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-emerald-600 mr-2 sm:mr-3 flex-shrink-0" />
                <span className="text-xs sm:text-sm text-gray-700 break-all">
                  <EMAIL>
                </span>
              </div>
              <div className="flex items-center justify-center lg:justify-start">
                <MapPin className="h-4 w-4 sm:h-5 sm:w-5 text-emerald-600 mr-2 sm:mr-3 flex-shrink-0" />
                <span className="text-xs sm:text-sm text-gray-700">
                  London, United Kingdom
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Partner;
