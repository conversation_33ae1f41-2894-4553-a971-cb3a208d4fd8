/*
  # Update schema to use arrays for multi-value fields

  1. Schema Changes
    - `pitch_deck_requests.partner_type` → array of text
    - `contact_messages.subject` → array of text  
    - `early_access_leads.role` → array of text

  2. Data Migration
    - Convert existing single values to arrays
    - Preserve all existing data

  3. Update Logic
    - Add new values to arrays if not already present
    - Avoid duplications
*/

-- Update pitch_deck_requests table to use array for partner_type
ALTER TABLE pitch_deck_requests 
ADD COLUMN IF NOT EXISTS partner_type_array text[] DEFAULT '{}';

-- Migrate existing data to array format
UPDATE pitch_deck_requests 
SET partner_type_array = ARRAY[partner_type]
WHERE partner_type_array = '{}' AND partner_type IS NOT NULL;

-- Drop the old column and rename the new one
ALTER TABLE pitch_deck_requests DROP COLUMN IF EXISTS partner_type;
ALTER TABLE pitch_deck_requests RENAME COLUMN partner_type_array TO partner_type;

-- Update contact_messages table to use array for subject
ALTER TABLE contact_messages 
ADD COLUMN IF NOT EXISTS subject_array text[] DEFAULT '{}';

-- Migrate existing data to array format
UPDATE contact_messages 
SET subject_array = ARRAY[subject]
WHERE subject_array = '{}' AND subject IS NOT NULL;

-- Drop the old column and rename the new one
ALTER TABLE contact_messages DROP COLUMN IF EXISTS subject;
ALTER TABLE contact_messages RENAME COLUMN subject_array TO subject;

-- Update early_access_leads table to use array for role
ALTER TABLE early_access_leads 
ADD COLUMN IF NOT EXISTS role_array text[] DEFAULT '{}';

-- Migrate existing data to array format
UPDATE early_access_leads 
SET role_array = ARRAY[role]
WHERE role_array = '{}' AND role IS NOT NULL;

-- Drop the old column and rename the new one
ALTER TABLE early_access_leads DROP COLUMN IF EXISTS role;
ALTER TABLE early_access_leads RENAME COLUMN role_array TO role;