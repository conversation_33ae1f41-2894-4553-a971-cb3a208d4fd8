import React from "react";
import { Button } from "../components/ui";

const ServerErrorPage: React.FC = () => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-800">505</h1>
        <p className="text-2xl font-light text-gray-600">
          Internal Server Error
        </p>
        <p className="mt-4 text-gray-500">
          Sorry, something went wrong on our end. Please try again later.
        </p>
        <Button
          variant="primary"
          size="lg"
          onClick={() => (window.location.href = "/")}
          className="mt-8"
        >
          Go Home
        </Button>
      </div>
    </div>
  );
};

export default ServerErrorPage;
